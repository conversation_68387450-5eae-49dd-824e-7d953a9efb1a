package dev.journey.PathSeeker.automation.transport;

import java.util.ArrayList;
import java.util.List;

/**
 * Represents a sequence of commands to be executed in order with configurable delays
 */
public class CommandSequence {
    public static class CommandStep {
        private final String command;
        private final int delayTicks;
        private final String description;

        public CommandStep(String command, int delayTicks, String description) {
            this.command = command;
            this.delayTicks = delayTicks;
            this.description = description;
        }

        public String getCommand() {
            return command;
        }

        public int getDelayTicks() {
            return delayTicks;
        }

        public String getDescription() {
            return description;
        }
    }

    private final String name;
    private final List<CommandStep> steps;
    private int currentStep;
    private int currentDelay;

    public CommandSequence(String name) {
        this.name = name;
        this.steps = new ArrayList<>();
        this.currentStep = 0;
        this.currentDelay = 0;
    }

    /**
     * Add a command step to the sequence
     * @param command Command to execute
     * @param delayTicks Ticks to wait after executing
     * @param description Description of what the command does
     * @return this for chaining
     */
    public CommandSequence addStep(String command, int delayTicks, String description) {
        steps.add(new CommandStep(command, delayTicks, description));
        return this;
    }

    /**
     * Reset the sequence to start from the beginning
     */
    public void reset() {
        currentStep = 0;
        currentDelay = 0;
    }

    /**
     * Get the next command to execute, or null if sequence is complete
     * @return Next command or null if done
     */
    public String getCurrentCommand() {
        if (currentStep >= steps.size()) return null;
        return steps.get(currentStep).getCommand();
    }

    /**
     * Get the current command step, or null if sequence is complete
     * @return Current step or null if done
     */
    public CommandStep getCurrentStep() {
        if (currentStep >= steps.size()) return null;
        return steps.get(currentStep);
    }

    /**
     * Update the sequence timer and return if it's time to execute the next command
     * @return true if next command should be executed
     */
    public boolean update() {
        if (currentStep >= steps.size()) return false;

        if (currentDelay > 0) {
            currentDelay--;
            return false;
        }

        return true;
    }

    /**
     * Advance to the next command
     */
    public void nextCommand() {
        if (currentStep >= steps.size()) return;
        currentDelay = steps.get(currentStep).getDelayTicks();
        currentStep++;
    }

    /**
     * Check if sequence has completed
     */
    public boolean isComplete() {
        return currentStep >= steps.size();
    }

    /**
     * Get sequence name
     */
    public String getName() {
        return name;
    }

    /**
     * Get all steps in sequence
     */
    public List<CommandStep> getSteps() {
        return new ArrayList<>(steps);
    }
}
