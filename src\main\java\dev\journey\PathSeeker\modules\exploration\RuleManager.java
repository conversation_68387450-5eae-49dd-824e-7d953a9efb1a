package dev.journey.PathSeeker.modules.exploration;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import net.fabricmc.loader.api.FabricLoader;

import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;

public class RuleManager {
    private static final Gson GSON = new GsonBuilder().setPrettyPrinting().create();
    private final List<SearchRule> rules;
    private final Path configFile;

    public RuleManager() {
        rules = new ArrayList<>();
        configFile = FabricLoader.getInstance().getConfigDir()
            .resolve("pathseeker")
            .resolve("rules.json");

        loadRules();
    }

    private void loadRules() {
        try {
            Files.createDirectories(configFile.getParent());
            
            if (Files.exists(configFile)) {
                String json = Files.readString(configFile);
                List<SearchRule> loaded = GSON.fromJson(json, new TypeToken<List<SearchRule>>(){}.getType());
                if (loaded != null) {
                    rules.clear();
                    rules.addAll(loaded);
                }
            }
        } catch (Exception e) {
            System.err.println("Failed to load rules: " + e.getMessage());
        }
    }

    public void saveRules() {
        try {
            String json = GSON.toJson(rules);
            Files.writeString(configFile, json);
        } catch (Exception e) {
            System.err.println("Failed to save rules: " + e.getMessage());
        }
    }

    public List<SearchRule> getRules() {
        return new ArrayList<>(rules);
    }

    public void addRule(SearchRule rule) {
        if (rule != null && rule.isValid()) {
            rules.add(rule);
            saveRules();
        }
    }

    public void removeRule(SearchRule rule) {
        if (rules.remove(rule)) {
            saveRules();
        }
    }

    public boolean shareRule(SearchRule rule, String discordWebhook) {
        // Basic sharing - just save for now
        if (rule != null && rule.isValid()) {
            addRule(rule);
            return true;
        }
        return false;
    }
}