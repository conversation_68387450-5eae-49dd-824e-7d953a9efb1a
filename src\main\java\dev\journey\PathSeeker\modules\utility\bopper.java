package dev.journey.PathSeeker.modules.utility;

import baritone.api.BaritoneAPI;
import baritone.api.IBaritone;
import baritone.api.pathing.goals.GoalXZ;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.item.Items;
import net.minecraft.network.packet.c2s.play.ClientCommandC2SPacket;
import net.minecraft.util.math.*;
import net.minecraft.sound.SoundEvents;
import net.minecraft.sound.SoundCategory;
import net.minecraft.item.ItemStack;
import net.minecraft.entity.EquipmentSlot;
import dev.journey.PathSeeker.PathSeeker;

public class bopper extends Module {
    private final SettingGroup sgGeneral = settings.getDefaultGroup();
    private final SettingGroup sgTakeoff = settings.createGroup("Takeoff");
    private final SettingGroup sgFlight = settings.createGroup("Flight");

    // General Settings
    private final Setting<Double> minHeight = sgGeneral.add(new DoubleSetting.Builder()
        .name("min-activation-height")
        .description("Minimum height before the module activates.")
        .defaultValue(100)
        .range(0, 320)
        .sliderRange(0, 320)
        .build()
    );

    private final Setting<TravelMode> travelMode = sgGeneral.add(new EnumSetting.Builder<TravelMode>()
        .name("travel-mode")
        .description("The mode to use for traveling.")
        .defaultValue(TravelMode.Overworld)
        .build()
    );

    private final Setting<Boolean> avoidLava = sgGeneral.add(new BoolSetting.Builder()
        .name("avoid-lava")
        .description("Avoids flying into lava.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> directional = sgGeneral.add(new BoolSetting.Builder()
        .name("directional")
        .description("Flies in the direction you're facing.")
        .defaultValue(false)
        .build()
    );

    // Takeoff Settings
    private final Setting<TakeoffMode> takeoffMode = sgTakeoff.add(new EnumSetting.Builder<TakeoffMode>()
        .name("takeoff-mode")
        .description("The method to use for taking off.")
        .defaultValue(TakeoffMode.Jump)
        .build()
    );

    private final Setting<Boolean> highPingOptimize = sgTakeoff.add(new BoolSetting.Builder()
        .name("high-ping-optimize")
        .description("Optimizes takeoff for high ping.")
        .defaultValue(false)
        .build()
    );

    // Flight Settings
    private final Setting<Double> cruisingHeight = sgFlight.add(new DoubleSetting.Builder()
        .name("cruising-height")
        .description("Target height to maintain during flight.")
        .defaultValue(120)
        .range(80, 260)
        .sliderRange(80, 260)
        .build()
    );

    private final Setting<Double> heightTolerance = sgFlight.add(new DoubleSetting.Builder()
        .name("height-tolerance")
        .description("How much height variation is allowed.")
        .defaultValue(2.0)
        .range(0.5, 10.0)
        .sliderRange(0.5, 10.0)
        .build()
    );

    private BlockPos goal;
    private int jumpY = -1;
    private boolean waitingForHeight = false;
    private boolean pathingInProgress = false;
    private final IBaritone baritone;
    private double yTarget = -1;
    private boolean launched = false;
    private float targetPitch = 0;

    public bopper() {
        super(PathSeeker.Utility, "bopper", "Automated elytra flight using Baritone pathfinding.");
        this.baritone = BaritoneAPI.getProvider().getPrimaryBaritone();
        BaritoneAPI.getSettings().logger.value = this::info;
    }

    @Override
    public void onActivate() {
        if (mc.player == null) return;

        if (directional.get()) {
            Vec3i dir = mc.player.getHorizontalFacing().getVector();
            goal = mc.player.getBlockPos().add(dir.multiply(6942069));
        } else if (goal == null) {
            error("You need to set coordinates first! Use the min-height setting to activate at a specific height.");
            toggle();
            return;
        }

        jumpY = -1;
        waitingForHeight = true;
        pathingInProgress = false;
        launched = false;
        yTarget = -1;
    }

    @Override
    public void onDeactivate() {
        baritone.getCommandManager().execute("stop");
        pathingInProgress = false;
    }

    @EventHandler
    private void onTick(TickEvent.Post event) {
        if (mc.player == null || goal == null) return;

        // Wait for minimum height
        if (waitingForHeight) {
            if (mc.player.getY() < minHeight.get()) {
                return;
            }
            waitingForHeight = false;
        }

        // Check if goal is reached
        if (mc.player.getPos().distanceTo(Vec3d.of(goal)) < 15) {
            mc.world.playSound(mc.player.getX(), mc.player.getY(), mc.player.getZ(), 
                SoundEvents.ENTITY_PLAYER_LEVELUP, SoundCategory.AMBIENT, 100.0f, 18.0f, true);
            info("Goal reached!");
            toggle();
            return;
        }

        // Check elytra
        if (!checkElytra()) {
            error("No usable elytra found!");
            toggle();
            return;
        }

        // Handle takeoff and flight
        if (!mc.player.getAbilities().flying) {
            handleTakeoff();
        } else {
            handleFlight();
            if (!pathingInProgress) {
                startBaritonePathing();
            }
        }
    }

    private boolean checkElytra() {
        ItemStack elytra = mc.player.getEquippedStack(EquipmentSlot.CHEST);
        return elytra.getItem() == Items.ELYTRA && (elytra.getMaxDamage() - elytra.getDamage()) > 3;
    }

    private void handleTakeoff() {
        if (mc.player.isOnGround()) {
            jumpY = MathHelper.floor(mc.player.getY());
            mc.player.jump();
            launched = false;
        } else {
            if (takeoffMode.get() == TakeoffMode.SlowGlide) {
                mc.player.setVelocity(0, -0.04, 0);
            } else {
                attemptElytraStart();
            }
        }
    }

    private void handleFlight() {
        if (mc.player.isGliding()) {
            if (yTarget == -1 || !launched) {
                yTarget = cruisingHeight.get();
                launched = true;
            }

            double currentY = mc.player.getY();
            double yDiff = currentY - yTarget;

            // Adjust pitch based on height difference
            if (Math.abs(yDiff) > 10.0) {
                targetPitch = (float) (-Math.atan2(yDiff, 100) * (180 / Math.PI));
            } else if (yDiff > heightTolerance.get()) {
                targetPitch = 10f;
            } else if (yDiff < -heightTolerance.get()) {
                targetPitch = -10f;
            } else {
                targetPitch = 0f;
            }

            // Smooth pitch adjustment
            float currentPitch = mc.player.getPitch();
            float pitchDiff = targetPitch - currentPitch;
            mc.player.setPitch(currentPitch + pitchDiff * 0.1f);

            // Let Baritone handle yaw rotation for pathfinding
        }
    }

    private void attemptElytraStart() {
        boolean closeToGround = mc.player.getY() <= mc.world.getBottomY();

        if (mc.player.getVelocity().y < (highPingOptimize.get() ? 0 : -0.02)) {
            if (!closeToGround) {
                mc.player.networkHandler.sendPacket(new ClientCommandC2SPacket(mc.player, 
                    ClientCommandC2SPacket.Mode.START_FALL_FLYING));
            }
        }
    }

    private void startBaritonePathing() {
        // Set Baritone goal and configure for elytra flight
        baritone.getCommandManager().execute("stop");
        baritone.getCustomGoalProcess().setGoalAndPath(new GoalXZ(goal.getX(), goal.getZ()));
        baritone.getCommandManager().execute("elytra");
        pathingInProgress = true;
    }

    public enum TravelMode {
        Highway,
        Overworld
    }

    public enum TakeoffMode {
        SlowGlide,
        Jump
    }

    public void setGoal(BlockPos goal) {
        this.goal = goal;
        info("Destination set to: " + goal.toShortString());
        if (isActive()) {
            pathingInProgress = false;
            startBaritonePathing();
        }
    }
}