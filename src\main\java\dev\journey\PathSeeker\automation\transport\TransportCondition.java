package dev.journey.PathSeeker.automation.transport;

import dev.journey.PathSeeker.automation.AutomationFlow;
import dev.journey.PathSeeker.automation.conditions.Condition;

/**
 * Represents a transport-specific condition with detailed failure reporting
 */
public interface TransportCondition extends Condition {
    /**
     * Test if the condition is met
     * @param flow The automation flow context
     * @return true if condition is satisfied
     */
    @Override
    boolean test(AutomationFlow flow);

    /**
     * Get the reason why the condition failed
     * @return A detailed message explaining the failure
     */
    String getFailureReason();

    /**
     * Get the severity level of the failure
     * @return TransportFailureSeverity indicating how critical the failure is
     */
    TransportFailureSeverity getSeverity();

    /**
     * Enumeration of possible failure severity levels
     */
    enum TransportFailureSeverity {
        /** Minor issue that can be retried */
        RECOVERABLE,
        
        /** Serious issue requiring user intervention */
        WARNING,
        
        /** Critical failure requiring immediate stop */
        CRITICAL
    }
}