package dev.journey.PathSeeker.modules.automation;

import dev.journey.PathSeeker.PathSeeker;
import dev.journey.PathSeeker.automation.AutomationFlow;
import dev.journey.PathSeeker.automation.conditions.Condition;
import dev.journey.PathSeeker.automation.actions.Action;
import dev.journey.PathSeeker.automation.transport.*;
import dev.journey.PathSeeker.modules.utility.AutoLoginModule;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.events.game.GameLeftEvent;
import meteordevelopment.meteorclient.events.game.ReceiveMessageEvent;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.text.Text;
import net.minecraft.util.math.BlockPos;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class ShulkerTransportModule extends Module {
    private final SettingGroup sgGeneral = settings.getDefaultGroup();
    private final SettingGroup sgCommands = settings.createGroup("Commands");
    private final SettingGroup sgSequences = settings.createGroup("Sequences");
    private final SettingGroup sgSafety = settings.createGroup("Safety");
    private final SettingGroup sgPositions = settings.createGroup("Positions");
    private final SettingGroup sgStats = settings.createGroup("Statistics");
    private final SettingGroup sgSteps = settings.createGroup("Transport Steps");

    // Statistics tracking
    private int shulkersTransported = 0;
    private int enderpearlsUsed = 0;
    private int totalCommands = 0;
    private int failedAttempts = 0;
    private long startTime = 0;
    private int successfulTransports = 0;
    private int totalDistance = 0;
    private int lastPosX = 0;
    private int lastPosZ = 0;

    // Stats display settings
    private final Setting<Boolean> showStats = sgStats.add(new BoolSetting.Builder()
        .name("show-stats")
        .description("Show transport statistics")
        .defaultValue(true)
        .build()
    );

    private final Setting<List<String>> preTransportCommands = sgSteps.add(new StringListSetting.Builder()
        .name("pre-transport")
        .description("Commands to run before transport")
        .defaultValue(Arrays.asList(
            "/msg {other} Preparing transport",
            "/effect give {self} resistance 60 3"
        ))
        .build()
    );

    private final Setting<List<String>> mainTransportCommands = sgSteps.add(new StringListSetting.Builder()
        .name("main-transport")
        .description("Main transport sequence commands")
        .defaultValue(Arrays.asList(
            "/tp {other}",
            "/msg {other} Ready to transfer"
        ))
        .build()
    );

    private final Setting<List<String>> postTransportCommands = sgSteps.add(new StringListSetting.Builder()
        .name("post-transport")
        .description("Commands to run after transport")
        .defaultValue(Arrays.asList(
            "/msg {other} Transport complete",
            "/home"
        ))
        .build()
    );

    private final Setting<List<String>> errorRecoveryCommands = sgSteps.add(new StringListSetting.Builder()
        .name("error-recovery")
        .description("Commands to run on transport error")
        .defaultValue(Arrays.asList(
            "/msg {other} Transport failed",
            "/spawn"
        ))
        .build()
    );
    
    private final TransportConfig config;
    private final CoordinationManager coordinator;
    private final StasisController stasis;
    private final InventoryManager inventory;
    
    private CommandSequence currentSequence;
    private int currentDelay;
    private int currentRetries;
    private CommandSequence.CommandStep lastFailedStep;
    private TransportState currentState;
    private final List<AutomationFlow> activeFlows;

    // Swarm settings
    private Setting<Integer> swarmPort;
    private Setting<Boolean> setupCommanderSwarm;
    private Setting<Boolean> setupTransporterSwarm;

    private void initSwarmSettings() {
        swarmPort = sgCommands.add(new IntSetting.Builder()
            .name("swarm-port")
            .description("Port to use for swarm communication")
            .defaultValue(9543)
            .min(1000)
            .max(65535)
            .build()
        );

        setupCommanderSwarm = sgCommands.add(new BoolSetting.Builder()
            .name("setup-commander-swarm")
            .description("Setup swarm for commander role")
            .defaultValue(false)
            .onChanged(val -> {
                if (val) {
                    setupCommanderSwarm();
                    setupCommanderSwarm.set(false);
                }
            })
            .build()
        );

        setupTransporterSwarm = sgCommands.add(new BoolSetting.Builder()
            .name("setup-transporter-swarm")
            .description("Setup swarm for transporter role")
            .defaultValue(false)
            .onChanged(val -> {
                if (val) {
                    setupTransporterSwarm();
                    setupTransporterSwarm.set(false);
                }
            })
            .build()
        );
    }

    // Command settings
    private final Setting<List<String>> commanderCommands = sgCommands.add(new StringListSetting.Builder()
        .name("commander-commands")
        .description("Commands to be executed when playing as commander")
        .defaultValue(Arrays.asList(
            "/tpahere <transporter>",
            "/msg <transporter> ready",
            "/msg <transporter> done"
        ))
        .build()
    );

    private final Setting<List<String>> transporterCommands = sgCommands.add(new StringListSetting.Builder()
        .name("transporter-commands")
        .description("Commands to be executed when playing as transporter")
        .defaultValue(Arrays.asList(
            "/tpaccept",
            "/msg <commander> ready",
            "/msg <commander> complete"
        ))
        .build()
    );

    // Settings
    private final Setting<Integer> defaultCommandDelay = sgSequences.add(new IntSetting.Builder()
        .name("default-delay")
        .description("Default delay between commands in ticks")
        .defaultValue(20)
        .min(1)
        .max(200)
        .build()
    );

    private final Setting<String> commanderUsername = sgGeneral.add(new StringSetting.Builder()
        .name("commander-name")
        .description("Username of the commander")
        .defaultValue("")
        .build()
    );

    private final Setting<String> transporterUsername = sgGeneral.add(new StringSetting.Builder()
        .name("transporter-name")
        .description("Username of the transporter")
        .defaultValue("")
        .build()
    );

    private final Setting<Integer> readyMessageDelay = sgSequences.add(new IntSetting.Builder()
        .name("ready-delay")
        .description("Delay after ready messages in ticks")
        .defaultValue(40)
        .min(1)
        .max(200)
        .build()
    );

    private final Setting<Integer> teleportDelay = sgSequences.add(new IntSetting.Builder()
        .name("teleport-delay")
        .description("Delay after teleport commands in ticks")
        .defaultValue(60)
        .min(1)
        .max(200)
        .build()
    );

    private final Setting<Boolean> autoRetryCommands = sgSequences.add(new BoolSetting.Builder()
        .name("auto-retry")
        .description("Automatically retry failed commands")
        .defaultValue(false)
        .build()
    );

    private final Setting<Integer> maxRetries = sgSequences.add(new IntSetting.Builder()
        .name("max-retries")
        .description("Maximum retry attempts for failed commands")
        .defaultValue(3)
        .min(1)
        .max(10)
        .visible(() -> autoRetryCommands.get())
        .build()
    );

    private final Setting<Integer> retryDelay = sgSequences.add(new IntSetting.Builder()
        .name("retry-delay")
        .description("Delay between retry attempts in ticks")
        .defaultValue(40)
        .min(1)
        .max(200)
        .visible(() -> autoRetryCommands.get())
        .build()
    );

    private final Setting<Boolean> announceSteps = sgSequences.add(new BoolSetting.Builder()
        .name("announce-steps")
        .description("Announce sequence steps in chat")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> previewMode = sgSequences.add(new BoolSetting.Builder()
        .name("preview-mode")
        .description("Show command timings without executing")
        .defaultValue(false)
        .build()
    );

    private final Setting<Integer> moveCommandDelay = sgSequences.add(new IntSetting.Builder()
        .name("move-delay")
        .description("Delay after movement commands")
        .defaultValue(30)
        .min(1)
        .max(200)
        .build()
    );

    private final Setting<Integer> chatCommandDelay = sgSequences.add(new IntSetting.Builder()
        .name("chat-delay") 
        .description("Delay after chat messages")
        .defaultValue(10)
        .min(1)
        .max(200)
        .build()
    );

    private final Setting<Integer> combatCommandDelay = sgSequences.add(new IntSetting.Builder()
        .name("combat-delay")
        .description("Delay after combat commands")
        .defaultValue(15)
        .min(1)
        .max(200)
        .build()
    );

    public ShulkerTransportModule() {
        super(PathSeeker.Automation, "shulker-transport", "Automates shulker box transport between players");
        
        this.config = new TransportConfig();
        this.coordinator = new CoordinationManager(this, config);
        this.stasis = new StasisController(this, config);
        this.inventory = new InventoryManager(this, config);
        this.activeFlows = new ArrayList<>();
        this.currentState = TransportState.IDLE;
        
        // Initialize swarm settings
        initSwarmSettings();
    }

    private String getCommanderName() {
        return coordinator.getCommanderName();
    }

    private String getTransporterName() {
        return coordinator.getTransporterName();
    }

    @EventHandler
    public void onEnable() {
        if (mc.player == null) return;
        
        if (config.mode.get() == TransportMode.COMMANDER) {
            coordinator.setCommanderName(mc.player.getName().getString());
            coordinator.setTransporterName(transporterUsername.get());
        } else {
            coordinator.setCommanderName(commanderUsername.get());
            coordinator.setTransporterName(mc.player.getName().getString());
        }

        // Execute role-specific commands
        executeRoleCommands();
    }

    // New settings for StasisController
    private final Setting<Double> pitchSetting = sgSafety.add(new DoubleSetting.Builder()
        .name("pitch")
        .description("Pitch angle for stasis chamber")
        .defaultValue(90.0)
        .range(-90.0, 90.0)
        .build()
    );

    private final Setting<Double> yawSetting = sgSafety.add(new DoubleSetting.Builder()
        .name("yaw")
        .description("Yaw angle for stasis chamber")
        .defaultValue(0.0)
        .range(-180.0, 180.0)
        .build()
    );

    private List<String> getCommandsForRole() {
        return config.mode.get() == TransportMode.COMMANDER ?
               commanderCommands.get() : transporterCommands.get();
    }

    private final Setting<Integer> stasisDelay = sgSafety.add(new IntSetting.Builder()
        .name("stasis-delay")
        .description("Delay between stasis chamber operations")
        .defaultValue(10)
        .min(1)
        .max(100)
        .build()
    );

    private final Setting<BlockPos> trapdoorLocation = sgPositions.add(new BlockPosSetting.Builder()
        .name("trapdoor-location")
        .description("Location of the stasis chamber trapdoor")
        .defaultValue(new BlockPos(0, 64, 0))
        .build()
    );

    // Getter methods for StasisController
    public float getPitchSetting() {
        return pitchSetting.get().floatValue();
    }

    public float getYawSetting() {
        return yawSetting.get().floatValue();
    }

    public int getStasisDelay() {
        return stasisDelay.get();
    }

    public BlockPos getTrapdoorLocation() {
        return trapdoorLocation.get();
    }

    // Make executeCommand package-private for StasisController access
    public void executeCommand(String command) {
        if (mc.player == null) return;
        
        String finalCommand = command.startsWith("/") ? command.substring(1) : command;
        mc.player.networkHandler.sendCommand(finalCommand);
    }
private void executeSwarmSetup(boolean isCommander) {
    List<String> commands = new ArrayList<>();
    String targetName = isCommander ? getTransporterName() : getCommanderName();

    // Initial message
    if (isCommander) {
        commands.add(String.format("/msg %s Connections available", targetName));
    } else {
        commands.add(String.format("/msg %s ready to transport", targetName));
    }

    // Swarm configuration
    commands.add(".toggle swarm on");
    commands.add(String.format(".settings swarm mode %s", isCommander ? "Host" : "Worker"));
    commands.add(String.format(".settings swarm port %d", swarmPort.get()));
    
    // Execute commands
    executeCommandList(commands, targetName);
}

private void setupCommanderSwarm() {
    executeSwarmSetup(true);
}

private void setupTransporterSwarm() {
    executeSwarmSetup(false);
}

@EventHandler
private void onInit() {
    // Initialize settings if needed
    if (swarmPort == null) {
        info("Initializing swarm settings");
        initSwarmSettings();
    }
}

@EventHandler
private void onDisable() {
    // Clean up swarm when disabled
    if (isActive()) {
        executeCommand(".toggle swarm off");
    }
}


// Method for internal use (role-specific commands)
private void executeRoleCommands() {
    List<String> commands = getCommandsForRole();
    if (commands == null || commands.isEmpty()) {
        warning("No commands configured for current role");
        return;
    }

    CommandSequence sequence = new CommandSequence("role-commands");
    for (String cmd : commands) {
        String processedCmd = cmd.replace("<commander>", getCommanderName())
                                .replace("<transporter>", getTransporterName());
        sequence.addStep(processedCmd, defaultCommandDelay.get(), "Executing: " + cmd);
        updateStats("command");
    }
    
    currentSequence = sequence;
    currentRetries = 0;
    lastFailedStep = null;
}

public void executeCommandList(List<String> commands, String targetPlayer) {
    if (commands == null || commands.isEmpty()) {
        warning("No commands to execute");
        return;
    }

    CommandSequence sequence = new CommandSequence("command-list");
    for (String cmd : commands) {
        String processedCmd = cmd.replace("<commander>", getCommanderName())
                                .replace("<transporter>", getTransporterName());
        sequence.addStep(processedCmd, defaultCommandDelay.get(), "Executing: " + cmd);
    }

    currentSequence = sequence;
    currentRetries = 0;
    lastFailedStep = null;
}

@EventHandler
    private void onTick(TickEvent.Pre event) {
        if (!config.active.get() || mc.player == null || mc.world == null) return;

        try {
            coordinator.update();
            stasis.update();
            inventory.update();

            if (currentSequence != null) {
                if (currentDelay > 0) {
                    currentDelay--;
                    return;
                }

                if (currentSequence.update()) {
                    CommandSequence.CommandStep step = currentSequence.getCurrentStep();
                    String command = currentSequence.getCurrentCommand();
                    
                    if (command != null) {
                        if (previewMode.get()) {
                            info("Would execute: " + command);
                        } else {
                            if (announceSteps.get() && step != null && !step.getDescription().isEmpty()) {
                                info("Executing: " + step.getDescription());
                            }
                            executeCommand(command);
                            
                            // Track item usage and commands
                            if (command.contains("shulker")) {
                                updateStats("shulker");
                            } else if (command.contains("pearl") || command.contains("enderpearl")) {
                                updateStats("pearl");
                            }
                            updateStats("command");
                        }
                        
                        String cmd = command.toLowerCase();
                        if (cmd.startsWith("/tp") || cmd.contains("teleport")) {
                            currentDelay = teleportDelay.get();
                        } else if (cmd.contains("ready") || cmd.startsWith("/msg")) {
                            currentDelay = readyMessageDelay.get();
                        } else if (cmd.contains("move") || cmd.contains("walk")) {
                            currentDelay = moveCommandDelay.get();
                        } else if (cmd.startsWith("/attack") || cmd.contains("kill")) {
                            currentDelay = combatCommandDelay.get();
                        } else if (cmd.startsWith("/")) {
                            currentDelay = chatCommandDelay.get();
                        } else {
                            currentDelay = defaultCommandDelay.get();
                        }

                        currentSequence.nextCommand();
                    }
                }

                if (currentSequence.isComplete()) {
                    info("Command sequence completed");
                    currentSequence = null;
                    currentRetries = 0;
                    lastFailedStep = null;
                }
                return;
            }

            if (currentState == TransportState.IDLE) return;
            
            for (AutomationFlow currentFlow : activeFlows) {
                try {
                    currentFlow.execute();
                } catch (Exception execError) {
                    error("Flow execution failed: " + execError.getMessage());
                    continue;
                }
            }
        } catch (Exception e) {
            handleSequenceError(e);
            if (currentSequence != null) {
                warning("Command sequence failed: " + e.getMessage());
                currentSequence = null;
            }
        }
    }

    private void handleSequenceError(Exception e) {
        updateStats("fail");
        if (!autoRetryCommands.get() || currentSequence == null) {
            handleError(e);
            return;
        }

        if (currentRetries < maxRetries.get()) {
            currentRetries++;
            currentDelay = retryDelay.get();
            warning("Command failed, retry attempt " + currentRetries + "/" + maxRetries.get());
            
            if (lastFailedStep == null) {
                lastFailedStep = currentSequence.getCurrentStep();
            }
            
            currentSequence.reset();
            while (currentSequence.getCurrentStep() != lastFailedStep) {
                currentSequence.nextCommand();
            }
        } else {
            error("Max retries reached, aborting sequence");
            handleError(e);
            currentSequence = null;
            currentRetries = 0;
            lastFailedStep = null;
        }
    }

    private void handleError(Exception e) {
        error("Transport error: " + e.getMessage());
        if (e instanceof TransportException) {
            TransportException te = (TransportException) e;
            setState(te.getErrorState());
        } else {
            setState(TransportState.IDLE);
        }
    }

    private void updateDistance() {
        if (mc.player != null) {
            int currentX = mc.player.getBlockX();
            int currentZ = mc.player.getBlockZ();
            
            // Calculate Manhattan distance
            totalDistance += Math.abs(currentX - lastPosX) + Math.abs(currentZ - lastPosZ);
            
            lastPosX = currentX;
            lastPosZ = currentZ;
        }
    }

    public void setState(TransportState newState) {
        this.currentState = newState;
        info("State changed to: " + newState.getDisplayName());
    }

    public TransportConfig getConfig() {
        return config;
    }

    public TransportState getState() {
        return currentState;
    }

    public void sendTransportMessage(String message) {
        if (mc.player == null) return;
        mc.player.sendMessage(Text.literal("[Transport] " + message), false);
    }

    private void displayStats() {
        if (!showStats.get() || mc.player == null) return;

        long duration = System.currentTimeMillis() - startTime;
        String timeStr = String.format("%02d:%02d:%02d",
            duration / 3600000,
            (duration % 3600000) / 60000,
            (duration % 60000) / 1000);

        info("=== Transport Statistics ===");
        info(String.format("Shulkers Moved: %d", shulkersTransported));
        info(String.format("Enderpearls Used: %d", enderpearlsUsed));
        info(String.format("Commands Run: %d", totalCommands));
        info(String.format("Failed Attempts: %d", failedAttempts));
        info(String.format("Total Distance: %dm", totalDistance));
        info(String.format("Total Successful Transports: %d", successfulTransports));
        info(String.format("Transport Efficiency: %.2f shulkers/min",
             (duration > 0) ? (shulkersTransported * 60000.0f / duration) : 0));
        info("======================");
    }

    private void updateStats(String action) {
        switch (action) {
            case "shulker":
                shulkersTransported++;
                successfulTransports++;
                updateDistance();
                break;
            case "pearl":
                enderpearlsUsed++;
                break;
            case "command":
                totalCommands++;
                break;
            case "fail":
                failedAttempts++;
                break;
            case "success":
                successfulTransports++;
                updateDistance();
                break;
        }
        
        if (showStats.get()) {
            displayStats();
        }
    }

    private void resetStats() {
        shulkersTransported = 0;
        enderpearlsUsed = 0;
        totalCommands = 0;
        failedAttempts = 0;
        startTime = System.currentTimeMillis();
        if (showStats.get()) {
            info("Statistics reset");
        }
    }
}
