package dev.journey.PathSeeker.commands;

import com.mojang.brigadier.arguments.IntegerArgumentType;
import com.mojang.brigadier.builder.LiteralArgumentBuilder;
import meteordevelopment.meteorclient.commands.Command;
import meteordevelopment.meteorclient.systems.modules.Modules;
import net.minecraft.command.CommandSource;
import net.minecraft.util.math.BlockPos;
import dev.journey.PathSeeker.modules.utility.bopper;

import static com.mojang.brigadier.Command.SINGLE_SUCCESS;

public class BopCommand extends Command {
    public BopCommand() {
        super("bop", "Sets destination coordinates for bopper module");
    }

    @Override
    public void build(LiteralArgumentBuilder<CommandSource> builder) {
        builder.then(argument("x", IntegerArgumentType.integer())
            .then(argument("y", IntegerArgumentType.integer())
                .then(argument("z", IntegerArgumentType.integer())
                    .executes(context -> {
                        int x = context.getArgument("x", Integer.class);
                        int y = context.getArgument("y", Integer.class);
                        int z = context.getArgument("z", Integer.class);

                        bopper bopperMod = Modules.get().get(bopper.class);
                        if (bopperMod != null) {
                            bopperMod.setGoal(new BlockPos(x, y, z));
                            info("Destination set to: " + x + ", " + y + ", " + z);
                        } else {
                            error("Bopper module not found!");
                        }

                        return SINGLE_SUCCESS;
                    }))));
    }
}