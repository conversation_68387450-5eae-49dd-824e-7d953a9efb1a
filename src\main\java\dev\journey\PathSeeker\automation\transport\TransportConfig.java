package dev.journey.PathSeeker.automation.transport;

import meteordevelopment.meteorclient.settings.*;
import net.minecraft.item.Item;
import net.minecraft.util.math.BlockPos;
import java.util.*;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

/**
 * Configuration settings for the ShulkerTransport system
 */
public class TransportConfig {
    private final Settings settings = new Settings();
    private final SettingGroup sgGeneral = settings.getDefaultGroup();
    private final SettingGroup sgCommands = settings.createGroup("Commands");
    private final SettingGroup sgSafety = settings.createGroup("Safety");
    private final SettingGroup sgInventory = settings.createGroup("Inventory");
    private final SettingGroup sgPositions = settings.createGroup("Positions");

    // General Settings
    public final Setting<TransportMode> mode = sgGeneral.add(new EnumSetting.Builder<TransportMode>()
        .name("mode")
        .description("The role this instance will play in transport")
        .defaultValue(TransportMode.TRANSPORTER)
        .build()
    );

    public final Setting<Boolean> active = sgGeneral.add(new BoolSetting.Builder()
        .name("active")
        .description("Whether the transport system is currently running")
        .defaultValue(false)
        .build()
    );

    public final Setting<Integer> coordinationChannel = sgGeneral.add(new IntSetting.Builder()
        .name("coord-channel")
        .description("Secure channel for coordination messages")
        .defaultValue(1)
        .min(1)
        .max(999)
        .build()
    );

    // Safety Settings
    public final Setting<Integer> pearlTimeout = sgSafety.add(new IntSetting.Builder()
        .name("pearl-timeout")
        .description("Maximum ticks to wait for pearl teleport")
        .defaultValue(100)
        .min(20)
        .max(200)
        .build()
    );

    public final Setting<Integer> chamberTimeout = sgSafety.add(new IntSetting.Builder()
        .name("chamber-timeout")
        .description("Maximum ticks to wait for chamber activation")
        .defaultValue(60)
        .min(20)
        .max(200)
        .build()
    );

    public final Setting<Double> safetyRadius = sgSafety.add(new DoubleSetting.Builder()
        .name("safety-radius")
        .description("Required clear space around teleport positions")
        .defaultValue(2.0)
        .min(1.0)
        .max(5.0)
        .build()
    );

    // Inventory Settings
    private final Set<Item> blacklistedItems = new HashSet<>();

    public final Setting<Boolean> requireConfirmation = sgInventory.add(new BoolSetting.Builder()
        .name("require-confirmation")
        .description("Whether to require confirmation for item transfers")
        .defaultValue(true)
        .build()
    );

    public final Setting<Integer> maxShulkerBoxes = sgInventory.add(new IntSetting.Builder()
        .name("max-shulkers")
        .description("Maximum number of shulker boxes to transport at once")
        .defaultValue(1)
        .min(1)
        .max(27)
        .build()
    );

    // Position Settings
    public final Setting<BlockPos> chamberPos = sgPositions.add(new BlockPosSetting.Builder()
        .name("chamber-pos")
        .description("Position of the stasis chamber")
        .defaultValue(new BlockPos(0, 64, 0))
        .build()
    );

    public final Setting<BlockPos> loadingPos = sgPositions.add(new BlockPosSetting.Builder()
        .name("loading-pos")
        .description("Position for loading/unloading items")
        .defaultValue(new BlockPos(0, 64, 0))
        .build()
    );

    public final Setting<BlockPos> safePos = sgPositions.add(new BlockPosSetting.Builder()
        .name("safe-pos")
        .description("Fallback position for safety")
        .defaultValue(new BlockPos(0, 64, 0))
        .build()
    );

    /**
     * Add an item to the blacklist
     * @param item The item to blacklist
     */
    public void addBlacklistedItem(Item item) {
        blacklistedItems.add(item);
    }

    /**
     * Remove an item from the blacklist
     * @param item The item to remove
     */
    public void removeBlacklistedItem(Item item) {
        blacklistedItems.remove(item);
    }

    /**
     * Check if an item is blacklisted
     * @param item The item to check
     * @return true if the item is blacklisted
     */
    public boolean isItemBlacklisted(Item item) {
        return blacklistedItems.contains(item);
    }

    /**
     * Get all blacklisted items
     * @return Set of blacklisted items
     */
    public Set<Item> getBlacklistedItems() {
        return new HashSet<>(blacklistedItems);
    }

    /**
     * Clear all blacklisted items
     */
    public void clearBlacklist() {
        blacklistedItems.clear();
    }

    // Command Sequence Settings
    private final Map<TransportMode, List<CommandSequence>> commandSequences = new HashMap<>();
    private final Gson gson = new Gson();

    // Commander command settings
    public final Setting<List<String>> commanderStartSequence = sgCommands.add(new StringListSetting.Builder()
        .name("commander-start")
        .description("Commands executed by commander when starting transport")
        .defaultValue(Arrays.asList("/tpahere <transporter>", "/msg <transporter> ready"))
        .build()
    );

    public final Setting<List<String>> commanderEndSequence = sgCommands.add(new StringListSetting.Builder()
        .name("commander-end")
        .description("Commands executed by commander when ending transport")
        .defaultValue(Arrays.asList("/msg <transporter> done", "/tpahere <transporter>"))
        .build()
    );

    // Transporter command settings
    public final Setting<List<String>> transporterAcceptSequence = sgCommands.add(new StringListSetting.Builder()
        .name("transporter-accept")
        .description("Commands executed by transporter when accepting transport")
        .defaultValue(Arrays.asList("/tpaccept", "/msg <commander> ready"))
        .build()
    );

    public final Setting<List<String>> transporterCompleteSequence = sgCommands.add(new StringListSetting.Builder()
        .name("transporter-complete")
        .description("Commands executed by transporter when completing transport")
        .defaultValue(Arrays.asList("/msg <commander> complete", "/tpaccept"))
        .build()
    );

    // Backup JSON storage
    public final Setting<String> commandSequenceJson = sgCommands.add(new StringSetting.Builder()
        .name("command-sequences-json")
        .description("Backup JSON configuration of command sequences")
        .defaultValue("{}")
        .build()
    );

    /**
     * Add a command sequence for a specific mode
     */
    public void addCommandSequence(TransportMode mode, CommandSequence sequence) {
        commandSequences.computeIfAbsent(mode, k -> new ArrayList<>()).add(sequence);
        saveCommandSequences();
    }

    /**
     * Remove a command sequence by name
     */
    public void removeCommandSequence(TransportMode mode, String sequenceName) {
        List<CommandSequence> sequences = commandSequences.get(mode);
        if (sequences != null) {
            sequences.removeIf(seq -> seq.getName().equals(sequenceName));
            saveCommandSequences();
        }
    }

    /**
     * Get all command sequences for a mode
     */
    public List<CommandSequence> getCommandSequences(TransportMode mode) {
        return commandSequences.getOrDefault(mode, new ArrayList<>());
    }

    /**
     * Save command sequences to JSON setting
     */
    private void saveCommandSequences() {
        try {
            commandSequenceJson.set(gson.toJson(commandSequences));
        } catch (Exception e) {
            System.err.println("Failed to save command sequences: " + e.getMessage());
        }
    }

    /**
     * Load command sequences from JSON setting
     */
    public void loadCommandSequences() {
        try {
            String json = commandSequenceJson.get();
            if (json != null && !json.isEmpty()) {
                Map<TransportMode, List<CommandSequence>> loaded = gson.fromJson(json,
                    new TypeToken<Map<TransportMode, List<CommandSequence>>>(){}.getType());
                if (loaded != null) {
                    commandSequences.clear();
                    commandSequences.putAll(loaded);
                }
            }
        } catch (Exception e) {
            System.err.println("Failed to load command sequences: " + e.getMessage());
        }
    }
}
