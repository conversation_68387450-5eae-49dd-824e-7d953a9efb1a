package com.beelzebub4883.skylandia_printify;

import meteordevelopment.meteorclient.addons.MeteorAddon;
import meteordevelopment.meteorclient.systems.modules.Category;
import meteordevelopment.meteorclient.systems.modules.Modules;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class Addon extends MeteorAddon {
	public static final Logger LOG = LogManager.getLogger();
	public static final Category CATEGORY = new Category("Printer", new ItemStack(Items.PINK_CARPET));

	@Override
	public void onInitialize() {
		LOG.info("Initializing Skylandia Printify");

		// Modules
		Modules.get().add(new Printer());
	}

    @Override
    public String getPackage() {
        return "com.beelzebub4883.skylandia_printify";
    }

	@Override
	public void onRegisterCategories() {
		Modules.registerCategory(CATEGORY);
	}
}
