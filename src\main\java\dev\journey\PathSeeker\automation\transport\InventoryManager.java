package dev.journey.PathSeeker.automation.transport;

import dev.journey.PathSeeker.modules.automation.ShulkerTransportModule;
import dev.journey.PathSeeker.automation.AutomationFlow;
import dev.journey.PathSeeker.automation.actions.Action;
import net.minecraft.block.ShulkerBoxBlock;
import net.minecraft.client.MinecraftClient;
import net.minecraft.inventory.Inventory;
import net.minecraft.item.Item;
import net.minecraft.item.ItemStack;
import net.minecraft.screen.GenericContainerScreenHandler;
import net.minecraft.screen.ScreenHandler;
import net.minecraft.util.math.BlockPos;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * Manages inventory operations for shulker box transport
 */
public class InventoryManager {
    private static final int TRANSFER_TIMEOUT = 5000; // 5 seconds
    private static final int CONFIRMATION_TIMEOUT = 30000; // 30 seconds

    private final ShulkerTransportModule module;
    private final TransportConfig config;
    private final MinecraftClient mc;

    private CompletableFuture<Boolean> pendingConfirmation;
    private Map<Integer, ItemStack> pendingTransfers;
    private long lastTransferTime;
    private boolean awaitingConfirmation;

    public InventoryManager(ShulkerTransportModule module, TransportConfig config) {
        this.module = module;
        this.config = config;
        this.mc = MinecraftClient.getInstance();
        this.pendingTransfers = new HashMap<>();
    }

    /**
     * Update manager state
     */
    public void update() {
        if (pendingConfirmation != null && !pendingConfirmation.isDone()) {
            long elapsed = System.currentTimeMillis() - lastTransferTime;
            if (elapsed > CONFIRMATION_TIMEOUT) {
                pendingConfirmation.complete(false);
                awaitingConfirmation = false;
            }
        }
    }

    /**
     * Validate inventories before transport
     */
    public TransportAction validateInventories() {
        return new TransportAction() {
            @Override
            public void execute(AutomationFlow flow) {
                if (!validateState("inventory validation")) {
                    throw TransportException.inventory("Invalid state for inventory validation", 
                        module.getState());
                }

                // Check player inventory space
                if (!hasRequiredSpace()) {
                    throw TransportException.inventory("Insufficient inventory space", 
                        module.getState());
                }

                // Check shulker box contents
                if (!validateShulkerContents()) {
                    throw TransportException.inventory("Invalid shulker box contents", 
                        module.getState());
                }

                lastTransferTime = System.currentTimeMillis();
            }

            @Override
            public boolean requiresConfirmation() {
                return false;
            }

            @Override
            public void rollback(AutomationFlow flow) {
                pendingTransfers.clear();
            }
        };
    }

    /**
     * Execute item transfer
     */
    public TransportAction executeTransfer() {
        return new TransportAction() {
            @Override
            public void execute(AutomationFlow flow) {
                if (!validateState("item transfer")) {
                    throw TransportException.inventory("Invalid state for item transfer", 
                        module.getState());
                }

                if (config.requireConfirmation.get()) {
                    requestTransferConfirmation();
                } else {
                    performTransfer();
                }

                lastTransferTime = System.currentTimeMillis();
            }

            @Override
            public boolean requiresConfirmation() {
                return config.requireConfirmation.get();
            }

            @Override
            public void rollback(AutomationFlow flow) {
                cancelTransfer();
            }
        };
    }

    /**
     * Wait for transfer completion
     */
    public TransportAction waitForTransfer() {
        return new TransportAction() {
            @Override
            public void execute(AutomationFlow flow) {
                if (!validateState("transfer wait")) {
                    throw TransportException.inventory("Invalid state for transfer wait", 
                        module.getState());
                }

                try {
                    if (pendingConfirmation != null) {
                        if (!pendingConfirmation.get(TRANSFER_TIMEOUT, TimeUnit.MILLISECONDS)) {
                            throw TransportException.inventory("Transfer confirmation timeout", 
                                module.getState());
                        }
                    }
                } catch (Exception e) {
                    throw TransportException.inventory("Transfer wait failed: " + e.getMessage(), 
                        module.getState());
                }
            }

            @Override
            public boolean requiresConfirmation() {
                return false;
            }

            @Override
            public void rollback(AutomationFlow flow) {
                cancelTransfer();
            }
        };
    }

    private boolean validateState(String operation) {
        if (mc.player == null || mc.world == null) {
            module.error("Player or world not available for " + operation);
            return false;
        }

        if (System.currentTimeMillis() - lastTransferTime < 1000) {
            module.warning("Transfer attempted too quickly");
            return false;
        }

        return true;
    }

    private boolean hasRequiredSpace() {
        if (mc.player == null) return false;

        // Count empty slots
        int emptySlots = 0;
        for (ItemStack stack : mc.player.getInventory().main) {
            if (stack.isEmpty()) emptySlots++;
        }

        return emptySlots >= config.maxShulkerBoxes.get();
    }

    private boolean validateShulkerContents() {
        ScreenHandler handler = mc.player.currentScreenHandler;
        if (!(handler instanceof GenericContainerScreenHandler)) return false;

        GenericContainerScreenHandler container = (GenericContainerScreenHandler) handler;
        int slots = container.getRows() * 9;

        // Verify this is a shulker box (27 slots)
        if (slots != 27) return false;

        // Check for blacklisted items
        for (int i = 0; i < 27; i++) {
            ItemStack stack = container.getSlot(i).getStack();
            if (!stack.isEmpty() && config.isItemBlacklisted(stack.getItem())) {
                return false;
            }
        }

        return true;
    }

    private void requestTransferConfirmation() {
        if (awaitingConfirmation) {
            throw TransportException.inventory("Already awaiting confirmation", 
                module.getState());
        }

        pendingConfirmation = new CompletableFuture<>();
        awaitingConfirmation = true;

        // Store current inventory state
        if (mc.player != null) {
            pendingTransfers.clear();
            for (int i = 0; i < mc.player.getInventory().main.size(); i++) {
                ItemStack stack = mc.player.getInventory().main.get(i);
                if (!stack.isEmpty()) {
                    pendingTransfers.put(i, stack.copy());
                }
            }
        }
    }

    private void performTransfer() {
        ScreenHandler handler = mc.player.currentScreenHandler;
        if (!(handler instanceof GenericContainerScreenHandler)) {
            throw TransportException.inventory("Container not open", module.getState());
        }

        GenericContainerScreenHandler container = (GenericContainerScreenHandler) handler;
        if (container.getRows() * 9 != 27) {
            throw TransportException.inventory("Not a shulker box", module.getState());
        }

        try {
            // Quick transfer items between inventories
            for (int i = 0; i < 27; i++) {
                ItemStack stack = container.getSlot(i).getStack();
                if (!stack.isEmpty() && !config.isItemBlacklisted(stack.getItem())) {
                    // Click slot with quick transfer (SHIFT + click)
                    mc.interactionManager.clickSlot(
                        container.syncId,
                        i,
                        0,
                        net.minecraft.screen.slot.SlotActionType.QUICK_MOVE,
                        mc.player
                    );
                }
            }
            return;
        } catch (Exception e) {
            module.error("Transfer failed: " + e.getMessage());
            throw e;
        }
    }

    private void cancelTransfer() {
        if (pendingConfirmation != null) {
            pendingConfirmation.complete(false);
        }
        
        // Restore previous inventory state
        if (mc.player != null && !pendingTransfers.isEmpty()) {
            // TODO: Implement inventory restoration
        }

        pendingTransfers.clear();
        awaitingConfirmation = false;
    }

    /**
     * Process transfer confirmation response
     */
    public void handleConfirmation(boolean confirmed) {
        if (!awaitingConfirmation) return;

        if (confirmed) {
            performTransfer();
        } else {
            cancelTransfer();
        }

        if (pendingConfirmation != null) {
            pendingConfirmation.complete(confirmed);
        }
        awaitingConfirmation = false;
    }
}