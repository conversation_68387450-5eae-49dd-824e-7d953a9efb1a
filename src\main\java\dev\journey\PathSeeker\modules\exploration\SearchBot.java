package dev.journey.PathSeeker.modules.exploration;

import dev.journey.PathSeeker.PathSeeker;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.screen.ChatScreen;
import net.minecraft.item.Items;
import meteordevelopment.meteorclient.utils.render.MeteorToast;
import meteordevelopment.meteorclient.events.render.Render3DEvent;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.renderer.ShapeMode;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.utils.player.Rotations;
import meteordevelopment.meteorclient.utils.render.color.Color;
import meteordevelopment.meteorclient.utils.render.color.SettingColor;
import meteordevelopment.orbit.EventHandler;
import org.lwjgl.glfw.GLFW;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.MathHelper;
import net.minecraft.util.math.Vec3d;
import net.minecraft.world.chunk.WorldChunk;
import xaeroplus.XaeroPlus;
import xaeroplus.event.ChunkDataEvent;
import xaeroplus.module.ModuleManager;
import xaeroplus.module.impl.PaletteNewChunks;

import java.util.*;

public class SearchBot extends Module {
    private final SettingGroup sgGeneral = settings.getDefaultGroup();
    private final SettingGroup sgControl = settings.createGroup("Control");
    private final SettingGroup sgTyping = settings.createGroup("Typing");
    private final SettingGroup sgSearch = settings.createGroup("Search");
    private final SettingGroup sgRender = settings.createGroup("Render");

    // Typing Settings
    private final Setting<Integer> charDelay = sgTyping.add(new IntSetting.Builder()
        .name("Char Delay")
        .description("Delay between typing each character")
        .defaultValue(2)
        .min(1)
        .sliderMax(10)
        .build()
    );

    private final Setting<Integer> enterDelay = sgTyping.add(new IntSetting.Builder()
        .name("Enter Delay")
        .description("Delay before pressing enter")
        .defaultValue(10)
        .min(1)
        .sliderMax(50)
        .build()
    );

    // General Settings
    private final Setting<CardinalDirection> direction = sgGeneral.add(new EnumSetting.Builder<CardinalDirection>()
        .name("Direction")
        .description("Cardinal direction to face")
        .defaultValue(CardinalDirection.NORTH)
        .build()
    );

    private final Setting<SearchPattern> pattern = sgGeneral.add(new EnumSetting.Builder<SearchPattern>()
        .name("Search Pattern")
        .description("The pattern to use for searching")
        .defaultValue(SearchPattern.SNAKE)
        .build()
    );

    // Control Settings
    private final Setting<Double> flySpeed = sgControl.add(new DoubleSetting.Builder()
        .name("Flight Speed")
        .description("Speed of flight movement")
        .defaultValue(1.0)
        .min(0.1)
        .sliderMax(5.0)
        .build()
    );

    private final Setting<Integer> searchRadius = sgControl.add(new IntSetting.Builder()
        .name("Search Radius")
        .description("Maximum search radius in chunks")
        .defaultValue(32)
        .min(8)
        .sliderMax(128)
        .build()
    );

    private final Setting<Boolean> returnToStart = sgControl.add(new BoolSetting.Builder()
        .name("Return To Start")
        .description("Return to starting position when done")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> avoidLava = sgControl.add(new BoolSetting.Builder()
        .name("Avoid Lava")
        .description("Try to avoid flying over lava")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> pauseOnDisconnect = sgControl.add(new BoolSetting.Builder()
        .name("Pause on Disconnect")
        .description("Pause the search pattern if disconnected")
        .defaultValue(true)
        .build()
    );

    // Search Settings
    private final Setting<Integer> pointLimit = sgSearch.add(new IntSetting.Builder()
        .name("Point Limit")
        .description("Maximum number of search points to generate")
        .defaultValue(100)
        .min(10)
        .sliderMax(500)
        .build()
    );

    private final Setting<Integer> gridSpacing = sgSearch.add(new IntSetting.Builder()
        .name("Grid Spacing")
        .description("Spacing between grid points in chunks")
        .defaultValue(4)
        .min(1)
        .sliderMax(16)
        .visible(() -> pattern.get() == SearchPattern.GRID)
        .build()
    );

    private final Setting<Integer> spiralSpacing = sgSearch.add(new IntSetting.Builder()
        .name("Spiral Spacing")
        .description("Spacing between spiral rings in chunks")
        .defaultValue(8)
        .min(2)
        .sliderMax(32)
        .visible(() -> pattern.get() == SearchPattern.SPIRAL)
        .build()
    );

    private final Setting<Integer> randomPoints = sgSearch.add(new IntSetting.Builder()
        .name("Random Points")
        .description("Number of random points to generate")
        .defaultValue(25)
        .min(5)
        .sliderMax(100)
        .visible(() -> pattern.get() == SearchPattern.RANDOM)
        .build()
    );

    private final Setting<Boolean> optimizePath = sgSearch.add(new BoolSetting.Builder()
        .name("Optimize Path")
        .description("Try to optimize the search path")
        .defaultValue(true)
        .build()
    );

    private final Setting<List<String>> customWaypoints = sgSearch.add(new StringListSetting.Builder()
        .name("Custom Waypoints")
        .description("List of waypoints for custom pattern (format: x,z)")
        .defaultValue(Collections.emptyList())
        .visible(() -> pattern.get() == SearchPattern.CUSTOM)
        .build()
    );

    // Render Settings
    private final Setting<Boolean> showPath = sgRender.add(new BoolSetting.Builder()
        .name("Show Path")
        .description("Renders the search pattern path")
        .defaultValue(true)
        .build()
    );

    private final Setting<PathStyle> pathStyle = sgRender.add(new EnumSetting.Builder<PathStyle>()
        .name("Path Style")
        .description("How to render the path")
        .defaultValue(PathStyle.FULL)
        .visible(() -> showPath.get())
        .build()
    );

    private final Setting<SettingColor> gridColor = sgRender.add(new ColorSetting.Builder()
        .name("Grid Color")
        .description("Color of the grid lines")
        .defaultValue(new SettingColor(0, 255, 0, 100))
        .visible(() -> showPath.get())
        .build()
    );

    private final Setting<SettingColor> currentPathColor = sgRender.add(new ColorSetting.Builder()
        .name("Current Path Color")
        .description("Color of the current path segment")
        .defaultValue(new SettingColor(255, 0, 0, 255))
        .build()
    );

    private final Setting<SettingColor> nextPathColor = sgRender.add(new ColorSetting.Builder()
        .name("Next Path Color")
        .description("Color of the upcoming path segment")
        .defaultValue(new SettingColor(0, 255, 0, 150))
        .build()
    );

    private final Setting<SettingColor> pathColor = sgRender.add(new ColorSetting.Builder()
        .name("Path Color")
        .description("Color of the search path")
        .defaultValue(new SettingColor(255, 0, 0, 150))
        .visible(() -> showPath.get())
        .build()
    );

    private final Setting<Integer> renderDistance = sgRender.add(new IntSetting.Builder()
        .name("Render Distance")
        .description("Distance to render path/grid")
        .defaultValue(128)
        .min(32)
        .sliderMax(512)
        .visible(() -> showPath.get())
        .build()
    );

    // State variables
    private List<Vec3d> searchPoints = new ArrayList<>();
    private final long CHUNK_CHECK_DELAY = 5000; // 5 seconds
    private long lastChunkCheckTime = 0;
    private int newChunksFound = 0;
    private int requiredNewChunks = 3;
    private int currentPointIndex = 0;
    private Vec3d startPos;
    private double targetYaw;
    private final ArrayDeque<Long> recentChunks = new ArrayDeque<>(64);
    private Random random = new Random();
    private boolean isPaused = false;
    private Vec3d lastRenderPos = null;

    // Command typing state
    private enum State {
        WAITING,
        TYPING,
        ENTER_DOWN,
        ENTER_UP
    }
    private State typingState = State.WAITING;
    private String currentText = "";
    private int typingTimer = 0;
    private String pendingCommand = "";

    public SearchBot() {
        super(PathSeeker.Hunting, "SearchBot", "Advanced systematic area search with multiple patterns.");
    }

    public enum CardinalDirection {
        NORTH("north"),
        SOUTH("south"),
        EAST("east"),
        WEST("west");

        final String command;

        CardinalDirection(String command) {
            this.command = command;
        }

        public void apply(SearchBot bot) {
            if (bot.mc.player != null) {
                bot.startTypingCommand("rotation set " + command);
            }
        }
    }

    public enum SearchPattern {
        SNAKE("Snake Pattern"),
        GRID("Grid Search"),
        SPIRAL("Spiral Search"),
        QUADRANT("Quadrant Search"),
        RANDOM("Random Search"),
        CUSTOM("Custom Pattern");

        final String title;
        SearchPattern(String title) {
            this.title = title;
        }
    }

    public enum PathStyle {
        FULL("Full Path"),
        CURRENT("Current Segment"),
        POINTS("Points Only");

        final String title;
        PathStyle(String title) {
            this.title = title;
        }
    }

    @Override
    public void onActivate() {
        if (mc.player == null) return;
        
        startPos = mc.player.getPos();
        resetSearch();
        XaeroPlus.EVENT_BUS.register(this);
    }

    private void resetSearch() {
        searchPoints = new ArrayList<>();
        currentPointIndex = 0;
        isPaused = false;
        lastRenderPos = null;

        // Generate limited number of points based on pattern
        int limit = Math.min(pointLimit.get(), 
            pattern.get() == SearchPattern.RANDOM ? randomPoints.get() : 500);

        switch (pattern.get()) {
            case SNAKE -> generateSnakePattern(limit);
            case GRID -> generateGridPattern(limit);
            case SPIRAL -> generateSpiralPattern(limit);
            case QUADRANT -> generateQuadrantPattern(limit);
            case RANDOM -> generateRandomPattern(limit);
            case CUSTOM -> generateCustomPattern(limit);
        }

        if (optimizePath.get() && pattern.get() != SearchPattern.SPIRAL) {
            optimizePath();
        }

        if (returnToStart.get()) {
            searchPoints.add(startPos);
        }
    }

    private void generateSnakePattern(int limit) {
        Vec3d current = startPos;
        double yaw = 180;
        boolean right = true;
        int points = 0;
        int spacing = gridSpacing.get() * 16;

        while (points < limit) {
            Vec3d next = current.add(Vec3d.fromPolar(0, (float)yaw).multiply(spacing));
            if (isPointInRange(next)) {
                searchPoints.add(next);
                current = next;
                points++;

                if (points % 4 == 0) {
                    yaw += right ? -90 : 90;
                    right = !right;
                }
            } else break;
        }
    }

    private void generateGridPattern(int limit) {
        int spacing = gridSpacing.get();
        int radius = Math.min(searchRadius.get(), (int)Math.sqrt(limit));
        
        for (int x = -radius; x <= radius; x += spacing) {
            for (int z = -radius; z <= radius; z += spacing) {
                if (searchPoints.size() >= limit) return;
                
                Vec3d point = startPos.add(x * 16, 0, z * 16);
                if (isPointInRange(point)) {
                    searchPoints.add(point);
                }
            }
        }
    }

    private void generateSpiralPattern(int limit) {
        int spacing = spiralSpacing.get();
        double angle = 0;
        double radius = 0;
        int points = 0;
        
        while (points < limit) {
            double x = startPos.x + radius * Math.cos(angle);
            double z = startPos.z + radius * Math.sin(angle);
            Vec3d point = new Vec3d(x, startPos.y, z);
            
            if (!isPointInRange(point)) break;
            
            searchPoints.add(point);
            points++;
            
            angle += Math.PI / 8;
            radius += spacing * Math.PI / 16;
        }
    }

    private void generateQuadrantPattern(int limit) {
        int pointsPerQuadrant = limit / 4;
        int spacing = gridSpacing.get();
        
        for (int quadrant = 0; quadrant < 4; quadrant++) {
            int startX = (quadrant % 2 == 0) ? 0 : -searchRadius.get();
            int startZ = (quadrant / 2 == 0) ? 0 : -searchRadius.get();
            int points = 0;
            
            for (int x = startX; Math.abs(x) <= searchRadius.get() && points < pointsPerQuadrant; x += spacing) {
                for (int z = startZ; Math.abs(z) <= searchRadius.get() && points < pointsPerQuadrant; z += spacing) {
                    Vec3d point = startPos.add(x * 16, 0, z * 16);
                    if (isPointInRange(point)) {
                        searchPoints.add(point);
                        points++;
                    }
                }
            }
        }
    }

    private void generateCustomPattern(int limit) {
        List<String> waypoints = customWaypoints.get();
        if (waypoints.isEmpty()) return;

        for (String waypoint : waypoints) {
            if (searchPoints.size() >= limit) break;
            
            String[] coords = waypoint.split(",");
            if (coords.length == 2) {
                try {
                    double x = Double.parseDouble(coords[0]);
                    double z = Double.parseDouble(coords[1]);
                    Vec3d point = new Vec3d(x, startPos.y, z);
                    if (isPointInRange(point)) {
                        searchPoints.add(point);
                    }
                } catch (NumberFormatException e) {
                    error("Invalid waypoint format: " + waypoint);
                }
            }
        }
    }

    private void generateRandomPattern(int limit) {
        int radius = searchRadius.get() * 16;
        int attempts = 0;
        int maxAttempts = limit * 2;

        while (searchPoints.size() < limit && attempts < maxAttempts) {
            double angle = random.nextDouble() * Math.PI * 2;
            double dist = random.nextDouble() * radius;
            
            Vec3d point = startPos.add(
                Math.cos(angle) * dist,
                0,
                Math.sin(angle) * dist
            );
            
            if (isPointInRange(point) && !isTooClose(point)) {
                searchPoints.add(point);
            }
            
            attempts++;
        }
    }

    private boolean isTooClose(Vec3d point) {
        double minDistance = gridSpacing.get() * 16;
        return searchPoints.stream()
            .anyMatch(p -> p.squaredDistanceTo(point) < minDistance * minDistance);
    }

    private boolean isPointInRange(Vec3d point) {
        double maxDist = searchRadius.get() * 16;
        return point.subtract(startPos).lengthSquared() <= maxDist * maxDist;
    }

    private void optimizePath() {
        if (searchPoints.size() < 3) return;

        List<Vec3d> optimized = new ArrayList<>();
        Set<Vec3d> unvisited = new HashSet<>(searchPoints);
        
        Vec3d current = unvisited.iterator().next();
        unvisited.remove(current);
        optimized.add(current);

        while (!unvisited.isEmpty()) {
            Vec3d nearest = null;
            double minDist = Double.MAX_VALUE;
            
            for (Vec3d point : unvisited) {
                double dist = current.squaredDistanceTo(point);
                if (dist < minDist) {
                    minDist = dist;
                    nearest = point;
                }
            }
            
            current = nearest;
            optimized.add(current);
            unvisited.remove(current);
        }

        searchPoints = optimized;
    }

    @Override
    public void onDeactivate() {
        XaeroPlus.EVENT_BUS.unregister(this);
        searchPoints.clear();
        recentChunks.clear();
    }

    private void startTypingCommand(String command) {
        pendingCommand = command;
        typingState = State.TYPING;
        currentText = "";
        typingTimer = 0;
    }

    private void updateTypingState() {
        if (mc.player == null || pendingCommand.isEmpty()) return;

        switch (typingState) {
            case TYPING -> {
                if (typingTimer >= charDelay.get()) {
                    typingTimer = 0;
                    if (currentText.length() < pendingCommand.length()) {
                        currentText = pendingCommand.substring(0, currentText.length() + 1);
                        if (mc.currentScreen instanceof ChatScreen) {
                            mc.setScreen(new ChatScreen(currentText));
                        } else {
                            mc.setScreen(new ChatScreen(""));
                        }
                    } else {
                        typingState = State.ENTER_DOWN;
                        typingTimer = enterDelay.get();
                    }
                }
                typingTimer++;
            }
            case ENTER_DOWN -> {
                if (typingTimer <= 0) {
                    if (mc.currentScreen instanceof ChatScreen chat) {
                        mc.keyboard.onKey(mc.getWindow().getHandle(), GLFW.GLFW_KEY_ENTER, 0, 1, 0);
                        chat.keyPressed(GLFW.GLFW_KEY_ENTER, 0, 0);
                        typingState = State.ENTER_UP;
                        typingTimer = 2;
                    }
                } else {
                    typingTimer--;
                }
            }
            case ENTER_UP -> {
                if (typingTimer <= 0) {
                    mc.keyboard.onKey(mc.getWindow().getHandle(), GLFW.GLFW_KEY_ENTER, 0, 0, 0);
                    pendingCommand = "";
                    typingState = State.WAITING;
                } else {
                    typingTimer--;
                }
            }
        }
    }

    @EventHandler
    private void onTick(TickEvent.Post event) {
        if (mc.player == null || searchPoints.isEmpty() || isPaused) return;

        // Update typing state if needed
        if (typingState != State.WAITING) {
            updateTypingState();
            return;
        }

        Vec3d target = searchPoints.get(currentPointIndex);
        Vec3d toTarget = target.subtract(mc.player.getPos());
        
        // Check if point reached
        if (toTarget.lengthSquared() < 16) { // Within 4 blocks
            // Check for new chunks before turning
            long currentTime = System.currentTimeMillis();
            if (currentTime - lastChunkCheckTime > CHUNK_CHECK_DELAY) {
                lastChunkCheckTime = currentTime;
                if (newChunksFound >= requiredNewChunks) {
                    info("Found " + newChunksFound + " new chunks, continuing current direction");
                    mc.getToastManager().add(new MeteorToast(Items.DIAMOND_PICKAXE,
                        "New Chunks Found",
                        "Found " + newChunksFound + " chunks, continuing path"
                    ));
                    newChunksFound = 0;
                } else {
                    currentPointIndex = (currentPointIndex + 1) % searchPoints.size();
                    if (currentPointIndex == 0 && !returnToStart.get()) {
                        this.toggle();
                        return;
                    }
                    newChunksFound = 0;
                }
            }
        }

        // Check for new chunks using TrailFollower's method
        WorldChunk chunk = (WorldChunk)mc.world.getChunk(mc.player.getBlockPos());
        long chunkLong = chunk.getPos().toLong();

        if (!recentChunks.contains(chunkLong)) {
            if (recentChunks.size() >= 64) {
                recentChunks.pollFirst();
            }
            recentChunks.add(chunkLong);

            boolean is119NewChunk = ModuleManager.getModule(PaletteNewChunks.class)
                    .isNewChunk(chunk.getPos().x, chunk.getPos().z, chunk.getWorld().getRegistryKey());

            if (!is119NewChunk) {
                newChunksFound++;
            }
        }

        // Update movement
        direction.get().apply(this);
        
        // Check for hazards if enabled
        if (avoidLava.get() && mc.player.isInLava()) {
            mc.player.setVelocity(0, 1, 0); // Move upward
            return;
        }
        
        Vec3d velocity = Vec3d.fromPolar(0, mc.player.getYaw()).multiply(flySpeed.get());
        mc.player.setVelocity(velocity.x, 0, velocity.z);
    }

    @EventHandler
    private void onRender(Render3DEvent event) {
        if (!showPath.get() || searchPoints.isEmpty()) return;

        // Only update render calculations every 20 ticks to save performance
        if (mc.player.age % 20 == 0 || lastRenderPos == null || 
            mc.player.getPos().squaredDistanceTo(lastRenderPos) > 256) {
            lastRenderPos = mc.player.getPos();
        }

        int renderDist = renderDistance.get();
        
        // Render based on selected style
        switch (pathStyle.get()) {
            case FULL -> renderFullPath(event, renderDist);
            case CURRENT -> renderCurrentSegment(event);
            case POINTS -> renderPoints(event, renderDist);
        }

        // Render grid if applicable
        if (pattern.get() == SearchPattern.GRID) {
            renderGrid(event);
        }
    }

    private void renderFullPath(Render3DEvent event, int renderDist) {
        Vec3d last = null;
        double maxDistSq = renderDist * renderDist;

        for (Vec3d point : searchPoints) {
            if (point.squaredDistanceTo(lastRenderPos) > maxDistSq) continue;

            if (last != null) {
                event.renderer.line(
                    last.x, last.y, last.z,
                    point.x, point.y, point.z,
                    pathColor.get()
                );
            }
            last = point;
        }
    }

    private void renderCurrentSegment(Render3DEvent event) {
        if (currentPointIndex >= searchPoints.size()) return;

        Vec3d current = mc.player.getPos();
        Vec3d target = searchPoints.get(currentPointIndex);

        // Render current segment with thicker line
        drawThickLine(event, current, target, currentPathColor.get());

        // Render next 3 segments with decreasing opacity
        int nextSegments = Math.min(3, searchPoints.size() - currentPointIndex - 1);
        Vec3d lastPoint = target;
        
        for (int i = 0; i < nextSegments; i++) {
            Vec3d nextTarget = searchPoints.get(currentPointIndex + i + 1);
            SettingColor color = nextPathColor.get();
            float opacity = 1.0f - (i * 0.25f);
            SettingColor adjustedColor = new SettingColor(
                color.r, color.g, color.b,
                (int)(color.a * opacity)
            );
            drawThickLine(event, lastPoint, nextTarget, adjustedColor);
            lastPoint = nextTarget;
        }
    }

    private void renderPoints(Render3DEvent event, int renderDist) {
        double maxDistSq = renderDist * renderDist;

        for (Vec3d point : searchPoints) {
            if (point.squaredDistanceTo(lastRenderPos) > maxDistSq) continue;

            event.renderer.box(
                point.x - 0.5, point.y - 0.5, point.z - 0.5,
                point.x + 0.5, point.y + 0.5, point.z + 0.5,
                pathColor.get(), pathColor.get(),
                ShapeMode.Lines,
                0
            );
        }
    }

    private void renderGrid(Render3DEvent event) {
        int spacing = gridSpacing.get() * 16;
        int radius = Math.min(renderDistance.get(), searchRadius.get() * 16);
        Vec3d center = lastRenderPos;

        // Draw grid lines within render distance
        for (int offset = -radius; offset <= radius; offset += spacing) {
            // Vertical lines
            event.renderer.line(
                center.x + offset, center.y, center.z - radius,
                center.x + offset, center.y, center.z + radius,
                gridColor.get()
            );

            // Horizontal lines
            event.renderer.line(
                center.x - radius, center.y, center.z + offset,
                center.x + radius, center.y, center.z + offset,
                gridColor.get()
            );
        }
    }
    private void drawThickLine(Render3DEvent event, Vec3d start, Vec3d end, SettingColor color) {
        // Main line
        event.renderer.line(
            start.x, start.y, start.z,
            end.x, end.y, end.z,
            color
        );

        // Additional lines for thickness
        double thickness = 0.15;
        for (int i = 0; i < 4; i++) {
            double angle = i * Math.PI / 2;
            double dx = Math.cos(angle) * thickness;
            double dz = Math.sin(angle) * thickness;

            event.renderer.line(
                start.x + dx, start.y, start.z + dz,
                end.x + dx, end.y, end.z + dz,
                color
            );
        }
    }
}
