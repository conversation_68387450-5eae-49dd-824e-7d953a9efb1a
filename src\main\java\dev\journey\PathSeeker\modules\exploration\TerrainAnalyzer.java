package dev.journey.PathSeeker.modules.exploration;

import dev.journey.PathSeeker.PathSeeker;
import meteordevelopment.meteorclient.events.game.GameJoinedEvent;
import meteordevelopment.meteorclient.events.render.Render3DEvent;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.renderer.ShapeMode;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.utils.render.color.Color;
import meteordevelopment.meteorclient.utils.render.color.SettingColor;
import meteordevelopment.meteorclient.gui.GuiTheme;
import meteordevelopment.meteorclient.gui.WindowScreen;
import meteordevelopment.meteorclient.gui.widgets.WWidget;
import meteordevelopment.meteorclient.gui.widgets.containers.WTable;
import meteordevelopment.meteorclient.gui.widgets.containers.WVerticalList;
import meteordevelopment.meteorclient.gui.widgets.input.WTextBox;
import meteordevelopment.meteorclient.gui.widgets.pressable.WButton;
import meteordevelopment.meteorclient.gui.widgets.pressable.WMinus;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.block.Block;
import net.minecraft.util.math.Vec3d;
import net.minecraft.block.Blocks;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Box;
import dev.journey.PathSeeker.utils.PathSeekerUtil;
import net.minecraft.util.math.ChunkPos;
import net.minecraft.world.chunk.WorldChunk;
import net.minecraft.registry.tag.BlockTags;
import net.minecraft.state.property.Properties;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import java.util.concurrent.*;
import static dev.journey.PathSeeker.utils.PathSeekerUtil.sendWebhook;

public class TerrainAnalyzer extends Module {
    private final SettingGroup sgGeneral = settings.getDefaultGroup();
    private final SettingGroup sgAnalysis = settings.createGroup("Analysis");
    private final SettingGroup sgRender = settings.createGroup("Render");
    private final SettingGroup sgRules = settings.createGroup("Custom Rules");
    private final SettingGroup sgPerformance = settings.createGroup("Performance");
    private final SettingGroup sgVisualization = settings.createGroup("Visualization");

    // Performance Settings
    private final Setting<Boolean> multiThreaded = sgPerformance.add(new BoolSetting.Builder()
        .name("multi-threaded")
        .description("Use multiple threads for scanning")
        .defaultValue(false)
        .build()
    );

    private final Setting<Integer> batchSize = sgPerformance.add(new IntSetting.Builder()
        .name("batch-size")
        .description("Number of chunks to process in each batch")
        .defaultValue(4)
        .min(1)
        .max(16)
        .build()
    );

    private final Setting<Integer> scanDelay = sgPerformance.add(new IntSetting.Builder()
        .name("scan-delay")
        .description("Ticks between scans")
        .defaultValue(20)
        .min(1)
        .max(100)
        .build()
    );

    private final Setting<Boolean> pauseOnHighUsage = sgPerformance.add(new BoolSetting.Builder()
        .name("pause-on-high-usage")
        .description("Pause scanning when memory/CPU usage is high")
        .defaultValue(true)
        .build()
    );

    // Visualization Settings
    private final Setting<Boolean> showScanArea = sgVisualization.add(new BoolSetting.Builder()
        .name("show-scan-area")
        .description("Show the area being scanned")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> showProgress = sgVisualization.add(new BoolSetting.Builder()
        .name("show-progress")
        .description("Show scan progress indicators")
        .defaultValue(true)
        .build()
    );

    private final Setting<SettingColor> scanAreaColor = sgVisualization.add(new ColorSetting.Builder()
        .name("scan-area-color")
        .description("Color of the scan area outline")
        .defaultValue(new Color(0, 255, 255, 75))
        .build()
    );


    // General Settings
    private final Setting<Integer> horizontalRange = sgGeneral.add(new IntSetting.Builder()
        .name("horizontal-range")
        .description("Horizontal range for terrain analysis.")
        .defaultValue(32)
        .min(8)
        .max(128)
        .sliderMax(128)
        .build()
    );

    private final Setting<Integer> verticalRange = sgGeneral.add(new IntSetting.Builder()
        .name("vertical-range")
        .description("Vertical range for terrain analysis.")
        .defaultValue(32)
        .min(8)
        .max(128)
        .sliderMax(128)
        .build()
    );

    private final Setting<Boolean> checkPatterns = sgGeneral.add(new BoolSetting.Builder()
        .name("check-patterns")
        .description("Check for common base entrance patterns.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> deepScan = sgGeneral.add(new BoolSetting.Builder()
        .name("deep-scan")
        .description("Perform deeper analysis of block patterns.")
        .defaultValue(false)
        .build()
    );

    private final Setting<String> webhookUrl = sgGeneral.add(new StringSetting.Builder()
        .name("webhook-url")
        .description("Discord webhook URL for suspicious terrain alerts.")
        .defaultValue("")
        .build()
    );

    private final Setting<Boolean> reportPatterns = sgGeneral.add(new BoolSetting.Builder()
        .name("report-patterns")
        .description("Send webhook alerts for detected base entrance patterns.")
        .defaultValue(true)
        .visible(() -> !webhookUrl.get().isEmpty())
        .build()
    );

    private final Setting<Integer> minSuspiciousForAlert = sgGeneral.add(new IntSetting.Builder()
        .name("min-suspicious-for-alert")
        .description("Minimum suspicious blocks to trigger webhook alert.")
        .defaultValue(15)
        .min(5)
        .max(50)
        .sliderMax(50)
        .visible(() -> !webhookUrl.get().isEmpty())
        .build()
    );
    
    // Custom Rules Settings
    private final Setting<String> ruleShareWebhook = sgRules.add(new StringSetting.Builder()
        .name("rule-share-webhook")
        .description("Discord webhook URL for sharing search rules")
        .defaultValue("")
        .build()
    );

    private final Setting<Boolean> useCustomRules = sgRules.add(new BoolSetting.Builder()
        .name("use-custom-rules")
        .description("Enable custom search rules")
        .defaultValue(true)
        .build()
    );

    // Analysis Settings
    private final Setting<Double> gradientThreshold = sgAnalysis.add(new DoubleSetting.Builder()
        .name("gradient-threshold")
        .description("Threshold for detecting unnatural terrain gradients.")
        .defaultValue(0.7)
        .min(0.1)
        .max(1.0)
        .sliderMax(1.0)
        .build()
    );

    private final Setting<Integer> minSuspiciousBlocks = sgAnalysis.add(new IntSetting.Builder()
        .name("min-suspicious-blocks")
        .description("Minimum number of suspicious blocks to trigger detection.")
        .defaultValue(5)
        .min(1)
        .max(20)
        .sliderMax(20)
        .build()
    );

    // Render Settings
    private final Setting<ShapeMode> shapeMode = sgRender.add(new EnumSetting.Builder<ShapeMode>()
        .name("shape-mode")
        .description("How suspicious areas are rendered.")
        .defaultValue(ShapeMode.Both)
        .build()
    );

    private final Setting<SettingColor> suspiciousColor = sgRender.add(new ColorSetting.Builder()
        .name("suspicious-color")
        .description("Color of suspicious areas.")
        .defaultValue(new SettingColor(255, 0, 0, 75))
        .build()
    );

    // Data structures
    // Block and Pattern Selection
    private final Setting<List<Block>> targetBlocks = sgAnalysis.add(new BlockListSetting.Builder()
        .name("target-blocks")
        .description("Blocks to specifically search for")
        .defaultValue(Arrays.asList(
            Blocks.OBSIDIAN,
            Blocks.CRYING_OBSIDIAN,
            Blocks.REINFORCED_DEEPSLATE,
            Blocks.ANCIENT_DEBRIS
        ))
        .build()
    );

    private final Setting<List<Block>> ignoredBlocks = sgAnalysis.add(new BlockListSetting.Builder()
        .name("ignored-blocks")
        .description("Blocks to ignore during search")
        .defaultValue(Arrays.asList(
            Blocks.DIRT,
            Blocks.STONE,
            Blocks.GRAVEL,
            Blocks.SAND
        ))
        .build()
    );

    private final Setting<Boolean> ignoreVillages = sgAnalysis.add(new BoolSetting.Builder()
        .name("ignore-villages")
        .description("Ignore village structures unless doors are open")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> ignoreNaturalStructures = sgAnalysis.add(new BoolSetting.Builder()
        .name("ignore-natural")
        .description("Ignore natural structures (desert temples, etc)")
        .defaultValue(false)
        .build()
    );

    // Data structures
    private final Map<ChunkPos, List<BlockPos>> suspiciousAreas = new ConcurrentHashMap<>();
    private final Map<ChunkPos, Set<BasePattern>> detectedPatterns = new ConcurrentHashMap<>();
    private final Set<Block> naturalBlocks = new HashSet<>();
    private final Set<Block> suspiciousBlocks = new HashSet<>();
    private int tickCounter = 0;
    
    private RuleManager ruleManager;
    private final Map<ChunkPos, List<SearchRule>> matchedRules = new ConcurrentHashMap<>();

    // Scan state management
    private boolean scanning = false;
    private boolean paused = false;
    private float scanProgress = 0;
    private int processedChunks = 0;
    private int totalChunksToScan = 0;
    private ChunkPos currentChunk = null;
    private double averageScanTime = 0;
    private final Queue<ChunkPos> scanQueue = new ConcurrentLinkedQueue<>();
    private final Map<ChunkPos, CompletableFuture<Void>> activeTasks = new ConcurrentHashMap<>();
    private final ExecutorService scanExecutor;

    public enum BasePattern {
        // Player-made patterns
        STAIR_DOWN("Stair Down", new Block[][]{
            {Blocks.AIR, Blocks.AIR},
            {Blocks.STONE_STAIRS, Blocks.AIR},
            {Blocks.STONE, Blocks.STONE_STAIRS}
        }, true),
        
        PISTON_DOOR("Piston Door", new Block[][]{
            {Blocks.PISTON, Blocks.REDSTONE_BLOCK},
            {Blocks.AIR, Blocks.AIR}
        }, true),
        
        TUNNEL_ENTRANCE("Tunnel Entrance", new Block[][]{
            {Blocks.AIR, Blocks.AIR, Blocks.AIR},
            {Blocks.STONE, Blocks.AIR, Blocks.STONE},
            {Blocks.STONE, Blocks.STONE, Blocks.STONE}
        }, true),
        
        // Natural patterns
        VILLAGE_HOUSE("Village House", new Block[][]{
            {Blocks.OAK_PLANKS, Blocks.OAK_PLANKS, Blocks.OAK_PLANKS},
            {Blocks.OAK_PLANKS, Blocks.OAK_DOOR, Blocks.OAK_PLANKS},
            {Blocks.COBBLESTONE, Blocks.COBBLESTONE, Blocks.COBBLESTONE}
        }, false),
        
        DESERT_TEMPLE("Desert Temple", new Block[][]{
            {Blocks.SANDSTONE, Blocks.SANDSTONE, Blocks.SANDSTONE},
            {Blocks.SANDSTONE, Blocks.AIR, Blocks.SANDSTONE},
            {Blocks.SANDSTONE, Blocks.CUT_SANDSTONE, Blocks.SANDSTONE}
        }, false);

        private final String displayName;
        private final Block[][] pattern;
        private final boolean isArtificial;

        BasePattern(String displayName, Block[][] pattern, boolean isArtificial) {
            this.displayName = displayName;
            this.pattern = pattern;
            this.isArtificial = isArtificial;
        }

        public String getDisplayName() {
            return displayName;
        }

        public Block[][] getPattern() {
            return pattern;
        }

        public boolean isArtificial() {
            return isArtificial;
        }
    }

    private boolean initialized = false;

    public TerrainAnalyzer() {
        super(PathSeeker.Hunting, "terrain-analyzer", "Analyzes terrain for suspicious modifications indicating player activity.");
        initNaturalBlocks();
        scanExecutor = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors(),
            r -> {
                Thread t = new Thread(r, "TerrainAnalyzer-Scanner");
                t.setDaemon(true);
                return t;
            }
        );
    }

    @Override
    public void onActivate() {
        if (!initialized) {
            try {
                this.ruleManager = new RuleManager();
                initialized = true;
            } catch (Exception e) {
                showError("Failed to initialize RuleManager: " + e.getMessage());
                toggle();
                return;
            }
        }
    }

    @Override
    public void onDeactivate() {
        try {
            cancelScan();

            // Save and cleanup
            if (ruleManager != null) {
                ruleManager.saveRules();
            }
            matchedRules.clear();
            suspiciousAreas.clear();
            detectedPatterns.clear();

        } catch (Exception e) {
            showError("Error during deactivation: " + e.getMessage());
        }
    }

    private void cancelScan() {
        scanning = false;
        paused = false;
        scanQueue.clear();
        activeTasks.values().forEach(task -> task.cancel(true));
        activeTasks.clear();
        currentChunk = null;
        processedChunks = 0;
        totalChunksToScan = 0;
        scanProgress = 0;
        showToast("Scan", "§cScan cancelled");
    }

    private void pauseScan() {
        paused = true;
        showToast("Scan", "§eScan paused");
    }

    private void resumeScan() {
        paused = false;
        showToast("Scan", "§aScan resumed");
    }

    private void updateProgress() {
        if (totalChunksToScan > 0) {
            scanProgress = (float)processedChunks / totalChunksToScan;
            if (showProgress.get()) {
                showToast("Progress", String.format("§7%d/%d chunks (%.1f%%)",
                    processedChunks, totalChunksToScan, scanProgress * 100));
            }
        }
    }

    private void initNaturalBlocks() {
        // Add natural blocks that commonly occur in terrain
        naturalBlocks.addAll(ignoredBlocks.get());
        
        // Add suspicious blocks from settings
        suspiciousBlocks.addAll(targetBlocks.get());

        // Add default suspicious blocks that are always included
        suspiciousBlocks.addAll(Arrays.asList(
            Blocks.DIAMOND_BLOCK,
            Blocks.NETHERITE_BLOCK,
            Blocks.COMMAND_BLOCK,
            Blocks.BEDROCK
        ));
    }

    @EventHandler
    private void onTick(TickEvent.Post event) {
        tickCounter++;
        if (tickCounter % 20 != 0) return; // Only run analysis every second

        if (mc.player == null || mc.world == null) return;

        if (paused || !scanning) return;

        // Check system resources if enabled
        if (pauseOnHighUsage.get() && Runtime.getRuntime().freeMemory() < 100_000_000) {
            pauseScan();
            return;
        }

        // Process scan queue in batches
        if (!scanQueue.isEmpty() && activeTasks.size() < batchSize.get()) {
            ChunkPos nextChunk = scanQueue.poll();
            if (nextChunk != null) {
                currentChunk = nextChunk;
                scheduleChunkScan(nextChunk);
            }
        }

        // Update progress
        if (showProgress.get() && tickCounter % 40 == 0) {  // Update every 2 seconds
            updateProgress();
        }

        // Queue new chunks if needed
        if (scanQueue.isEmpty() && activeTasks.isEmpty()) {
            queueNewChunks();
        }
    }

    private void scheduleChunkScan(ChunkPos pos) {
        if (!mc.world.isChunkLoaded(pos.x, pos.z)) return;
        
        CompletableFuture<Void> task = CompletableFuture.runAsync(() -> {
            try {
                WorldChunk chunk = (WorldChunk)mc.world.getChunk(pos.x, pos.z);
                if (chunk != null) {
                    analyzeTerrain(chunk);
                }
            } catch (Exception e) {
                showError("Failed to analyze chunk: " + e.getMessage());
            } finally {
                activeTasks.remove(pos);
                processedChunks++;
            }
        }, multiThreaded.get() ? scanExecutor : Runnable::run);
        
        activeTasks.put(pos, task);
    }

    private boolean checkVillageDoors(BlockPos center) {
        BlockPos.Mutable mutable = new BlockPos.Mutable();
        int range = 16;
        
        for (int x = -range; x <= range; x++) {
            for (int y = -5; y <= 5; y++) {
                for (int z = -range; z <= range; z++) {
                    mutable.set(center.getX() + x, center.getY() + y, center.getZ() + z);
                    if (isVillageDoor(mutable) && isDoorOpen(mutable)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    private boolean isVillageDoor(BlockPos pos) {
        Block block = mc.world.getBlockState(pos).getBlock();
        return block == Blocks.OAK_DOOR || block == Blocks.SPRUCE_DOOR ||
               block == Blocks.BIRCH_DOOR || block == Blocks.ACACIA_DOOR;
    }

    private boolean isDoorOpen(BlockPos pos) {
        return mc.world.getBlockState(pos).get(Properties.OPEN);
    }

    private boolean checkVillageBounds(BlockPos pos) {
        // Check for typical village blocks in area
        int count = 0;
        int threshold = 5;
        BlockPos.Mutable mutable = new BlockPos.Mutable();

        for (int x = -8; x <= 8; x++) {
            for (int z = -8; z <= 8; z++) {
                mutable.set(pos.getX() + x, pos.getY(), pos.getZ() + z);
                Block block = mc.world.getBlockState(mutable).getBlock();
                if (isVillageBlock(block)) {
                    count++;
                    if (count >= threshold) return true;
                }
            }
        }
        return false;
    }

    private boolean isVillageBlock(Block block) {
        return block == Blocks.COBBLESTONE || block == Blocks.OAK_PLANKS ||
               block == Blocks.DIRT_PATH || block == Blocks.FARMLAND;
    }

    private void queueNewChunks() {
        if (mc.player == null) return;
        
        BlockPos playerPos = mc.player.getBlockPos();
        int range = horizontalRange.get();
        
        // Clear old data
        scanQueue.clear();
        suspiciousAreas.clear();

        // Get village info if needed
        boolean inVillage = false;
        boolean doorOpen = false;
        if (ignoreVillages.get()) {
            inVillage = checkVillageBounds(playerPos);
            if (inVillage) {
                doorOpen = checkVillageDoors(playerPos);
                if (!doorOpen) {
                    showToast("Info", "§7Skipping village area - doors closed");
                    return;
                }
            }
        }
        
        // Queue chunks in spiral pattern from player
        List<ChunkPos> chunks = new ArrayList<>();
        for (int x = -range; x <= range; x += 16) {
            for (int z = -range; z <= range; z += 16) {
                BlockPos pos = playerPos.add(x, 0, z);
                chunks.add(new ChunkPos(pos));
            }
        }
        
        // Sort by distance to player for priority
        chunks.sort((a, b) -> {
            double distA = distanceToPlayer(a);
            double distB = distanceToPlayer(b);
            return Double.compare(distA, distB);
        });
        
        scanQueue.addAll(chunks);
        totalChunksToScan = chunks.size();
        scanning = true;
    }

    private double distanceToPlayer(ChunkPos pos) {
        if (mc.player == null) return Double.MAX_VALUE;
        double dx = (pos.x << 4) + 8 - mc.player.getX();
        double dz = (pos.z << 4) + 8 - mc.player.getZ();
        return Math.sqrt(dx * dx + dz * dz);
    }

    private void analyzeTerrain(WorldChunk chunk) {
        long startTime = System.nanoTime();
        ChunkPos chunkPos = chunk.getPos();
        List<BlockPos> suspicious = new ArrayList<>();
        int vRange = verticalRange.get();
        BlockPos.Mutable mutable = new BlockPos.Mutable();

        for (int x = 0; x < 16; x++) {
            for (int z = 0; z < 16; z++) {
                int surfaceY = getSurfaceLevel(chunk, x, z);
                
                try {
                    // Analyze gradient and block patterns around surface level
                    int minY = Math.max(surfaceY - vRange, chunk.getBottomY());
                    int maxY = Math.min(surfaceY + vRange, chunk.getHeight());
                    
                    for (int y = minY; y <= maxY; y++) {
                        mutable.set(chunkPos.getStartX() + x, y, chunkPos.getStartZ() + z);
                        if (mc.world.isChunkLoaded(mutable) && isBlockSuspicious(mutable)) {
                            suspicious.add(mutable.toImmutable());
                        }
                    }
                } catch (Exception e) {
                    showToast("Block Scan", "Error scanning blocks: " + e.getMessage());
                }
            }
        }

        if (suspicious.size() >= minSuspiciousBlocks.get()) {
            suspiciousAreas.put(chunkPos, suspicious);
            
            // Send webhook alert if criteria met
            if (!webhookUrl.get().isEmpty() && suspicious.size() >= minSuspiciousForAlert.get()) {
                sendTerrainAlert(chunkPos, suspicious);
            }
            
            // Update timing statistics
            long endTime = System.nanoTime();
            double scanTime = (endTime - startTime) / 1_000_000.0; // Convert to ms
            averageScanTime = averageScanTime * 0.95 + scanTime * 0.05; // Exponential moving average
        }
    }

    private int getSurfaceLevel(WorldChunk chunk, int x, int z) {
        BlockPos.Mutable mutable = new BlockPos.Mutable();
        for (int y = chunk.getHeight(); y >= chunk.getBottomY(); y--) {
            mutable.set(chunk.getPos().getStartX() + x, y, chunk.getPos().getStartZ() + z);
            if (!mc.world.getBlockState(mutable).isAir()) {
                return y;
            }
        }
        return chunk.getBottomY();
    }

    private boolean isBlockSuspicious(BlockPos pos) {
        if (mc.world == null) return false;
        try {
            Block block = mc.world.getBlockState(pos).getBlock();
        
        // Skip natural blocks
        if (naturalBlocks.contains(block)) return false;

        // Check if it's a known suspicious block
        if (suspiciousBlocks.contains(block)) return true;

        // Check if this is a potential entrance (blocks that shouldn't be underground)
        if (pos.getY() < 60 && block.getDefaultState().isIn(BlockTags.LOGS)) return true;

        // If deep scanning is enabled, check more complex patterns
        if (deepScan.get()) {
            // Check for hidden doors (air pockets in unusual places)
            if (block.equals(Blocks.AIR) && isAirPocketSuspicious(pos)) return true;
            
            // Check for redstone components
            if (block.getDefaultState().emitsRedstonePower()) return true;
        }

        // Check surrounding gradients
        double gradient = calculateLocalGradient(pos);
        if (gradient > gradientThreshold.get()) return true;

        // Check for common base entrance patterns
        if (checkPatterns.get() && matchesAnyPattern(pos)) return true;

            // Check custom rules
            if (useCustomRules.get() && initialized && matchesCustomRules(pos)) return true;

            return false;
        } catch (Exception e) {
            showToast("Block Check", "Error checking block: " + e.getMessage());
            return false;
        }
    }

    private double calculateLocalGradient(BlockPos pos) {
        double maxGradient = 0;
        BlockPos.Mutable mutable = new BlockPos.Mutable();

        // Check gradients in all directions
        for (int x = -1; x <= 1; x++) {
            for (int y = -1; y <= 1; y++) {
                for (int z = -1; z <= 1; z++) {
                    if (x == 0 && y == 0 && z == 0) continue;
                    
                    mutable.set(pos.getX() + x, pos.getY() + y, pos.getZ() + z);
                    Block neighborBlock = mc.world.getBlockState(mutable).getBlock();
                    
                    if (naturalBlocks.contains(neighborBlock)) {
                        double gradient = 1.0 / Math.sqrt(x*x + y*y + z*z);
                        maxGradient = Math.max(maxGradient, gradient);
                    }
                }
            }
        }

        return maxGradient;
    }

    private boolean isAirPocketSuspicious(BlockPos pos) {
        // Check if this air pocket is surrounded by solid blocks in an unnatural way
        int solidCount = 0;
        BlockPos.Mutable mutable = new BlockPos.Mutable();

        for (int x = -1; x <= 1; x++) {
            for (int y = -1; y <= 1; y++) {
                for (int z = -1; z <= 1; z++) {
                    if (x == 0 && y == 0 && z == 0) continue;
                    
                    mutable.set(pos.getX() + x, pos.getY() + y, pos.getZ() + z);
                    if (mc.world.getBlockState(mutable).isFullCube(mc.world, mutable)) {
                        solidCount++;
                    }
                }
            }
        }

        // If air pocket is mostly surrounded by solid blocks, it's suspicious
        return solidCount >= 22; // Out of 26 surrounding blocks
    }

    private boolean matchesAnyPattern(BlockPos pos) {
        for (BasePattern pattern : BasePattern.values()) {
            if (matchesPattern(pos, pattern.getPattern())) {
                ChunkPos chunkPos = new ChunkPos(pos);
                detectedPatterns.computeIfAbsent(chunkPos, k -> new HashSet<>()).add(pattern);
                
                // Handle natural structures
                if (!pattern.isArtificial()) {
                    if (ignoreNaturalStructures.get()) {
                        showToast("Structure", "§7Found " + pattern.getDisplayName() + " (ignored)");
                        return false;
                    } else {
                        showToast("Structure", "§eFound " + pattern.getDisplayName());
                    }
                }
                
                // Special handling for village houses
                if (pattern == BasePattern.VILLAGE_HOUSE && ignoreVillages.get()) {
                    boolean doorOpen = checkVillageDoors(pos);
                    if (!doorOpen) {
                        showToast("Village", "§7House found with closed door (ignored)");
                        return false;
                    }
                }
                
                return true;
            }
        }
        return false;
    }

    private boolean matchesPattern(BlockPos start, Block[][] pattern) {
        BlockPos.Mutable mutable = new BlockPos.Mutable();
        
        // Try matching pattern in different orientations
        for (int rotation = 0; rotation < 4; rotation++) {
            boolean matches = true;
            
            for (int y = 0; y < pattern.length; y++) {
                for (int x = 0; x < pattern[y].length; x++) {
                    int rotX = rotation % 2 == 0 ? x : pattern[y].length - 1 - x;
                    int rotZ = rotation < 2 ? 0 : 1;
                    
                    mutable.set(start.getX() + rotX, start.getY() - y, start.getZ() + rotZ);
                    Block expectedBlock = pattern[y][x];
                    Block actualBlock = mc.world.getBlockState(mutable).getBlock();
                    
                    if (expectedBlock != actualBlock) {
                        matches = false;
                        break;
                    }
                }
                if (!matches) break;
            }
            
            if (matches) return true;
        }
        
        return false;
    }

    @EventHandler
    private void onRender(Render3DEvent event) {
        // Render suspicious areas and pattern matches
        suspiciousAreas.forEach((chunkPos, positions) -> {
            positions.forEach(pos -> {
                Box box = new Box(pos);
                Color color = detectedPatterns.containsKey(chunkPos) ?
                    new Color(255, 0, 255, 75) : // Purple for pattern matches
                    suspiciousColor.get();
                event.renderer.box(box, color, color, shapeMode.get(), 0);
            });
        });

        if (scanning && mc.world != null) {
            // Show current scan area if enabled
            if (showScanArea.get() && currentChunk != null) {
                Box scanBox = new Box(
                    currentChunk.getStartX(), -64, currentChunk.getStartZ(),
                    currentChunk.getStartX() + 16, 320, currentChunk.getStartZ() + 16
                );
                event.renderer.box(scanBox, scanAreaColor.get(), null, ShapeMode.Lines, 0);
            }

            // Show scan progress
            if (showProgress.get()) {
                String status = String.format("%s%.1f%% (%d/%d) %.1fms/chunk",
                    paused ? "§ePAUSED " : "§aScanning ",
                    scanProgress * 100,
                    processedChunks,
                    totalChunksToScan,
                    averageScanTime);

                showToast("Progress", status);
            }
        }
    }

    private void sendTerrainAlert(ChunkPos chunkPos, List<BlockPos> suspicious) {
        StringBuilder message = new StringBuilder();
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        
        message.append("**Suspicious Terrain Detected!**\n");
        message.append("Time: ").append(timestamp).append("\n\n");
        
        // Location info
        int centerX = chunkPos.getStartX() + 8;
        int centerZ = chunkPos.getStartZ() + 8;
        String dimension = mc.world.getRegistryKey().getValue().getPath();
        
        message.append("**Location:**\n");
        message.append("- Dimension: ").append(dimension).append("\n");
        message.append("- Coordinates: ").append(centerX).append(", ").append(centerZ).append("\n");
        message.append("- Distance from spawn: ").append(String.format("%.1f",
            Math.sqrt(centerX * centerX + centerZ * centerZ))).append(" blocks\n\n");
        
        // Pattern detections
        Set<BasePattern> patterns = detectedPatterns.get(chunkPos);
        if (patterns != null && !patterns.isEmpty() && reportPatterns.get()) {
            message.append("**Detected Patterns:**\n");
            patterns.forEach(pattern ->
                message.append("- ").append(pattern.getDisplayName()).append("\n")
            );
            message.append("\n");
        }
        
        // Block statistics
        Map<Block, Integer> blockCounts = new HashMap<>();
        suspicious.forEach(pos -> {
            Block block = mc.world.getBlockState(pos).getBlock();
            blockCounts.merge(block, 1, Integer::sum);
        });
        
        message.append("**Suspicious Blocks:**\n");
        blockCounts.entrySet().stream()
            .sorted((e1, e2) -> e2.getValue().compareTo(e1.getValue()))
            .limit(10)
            .forEach(entry ->
                message.append("- ").append(entry.getKey().getName().getString())
                    .append(": ").append(entry.getValue()).append("\n")
            );
        
        // Send the webhook in a new thread
        new Thread(() -> {
            try {
                sendWebhook(webhookUrl.get(), "Terrain Analysis Alert", message.toString(), null, mc.player.getGameProfile().getName());
            } catch (Exception e) {
                showError("Failed to send webhook alert: " + e.getMessage());
            }
        }).start();
    }

    private boolean matchesCustomRules(BlockPos pos) {
        if (ruleManager == null) return false;
        
        ChunkPos chunkPos = new ChunkPos(pos);
        List<SearchRule> matching = new ArrayList<>();

        try {
            for (SearchRule rule : ruleManager.getRules()) {
                if (rule != null && matchesRule(pos, rule)) {
                    matching.add(rule);
                }
            }

            if (!matching.isEmpty()) {
                matchedRules.put(chunkPos, matching);
                return true;
            }
        } catch (Exception e) {
            showError("Error checking custom rules: " + e.getMessage());
        }

        return false;
    }

    private boolean matchesRule(BlockPos pos, SearchRule rule) {
        Block block = mc.world.getBlockState(pos).getBlock();

        // Check basic block match
        if (!rule.matchesBlock(block)) return false;

        // Check conditions
        SearchRule.SearchCondition cond = rule.condition;
        
        if (pos.getY() < cond.minY || pos.getY() > cond.maxY) return false;
        
        if (cond.minGradient > 0) {
            double gradient = calculateLocalGradient(pos);
            if (gradient < cond.minGradient) return false;
        }
        
        if (cond.minSurroundingSolid > 0) {
            int solidCount = countSurroundingSolidBlocks(pos);
            if (solidCount < cond.minSurroundingSolid) return false;
        }
        
        if (cond.checkRedstone && !block.getDefaultState().emitsRedstonePower()) {
            return false;
        }

        // Check pattern if defined
        Block[][] pattern = rule.getPatternBlocks();
        if (pattern != null) {
            boolean matched = false;
            for (int rot = 0; rot < rule.pattern.rotations; rot++) {
                if (checkPattern(pos, pattern, rot)) {
                    matched = true;
                    break;
                }
            }
            if (!matched) return false;
        }

        return true;
    }

    private int countSurroundingSolidBlocks(BlockPos pos) {
        int count = 0;
        BlockPos.Mutable mutable = new BlockPos.Mutable();

        for (int x = -1; x <= 1; x++) {
            for (int y = -1; y <= 1; y++) {
                for (int z = -1; z <= 1; z++) {
                    if (x == 0 && y == 0 && z == 0) continue;
                    mutable.set(pos.getX() + x, pos.getY() + y, pos.getZ() + z);
                    if (mc.world.getBlockState(mutable).isFullCube(mc.world, mutable)) {
                        count++;
                    }
                }
            }
        }

        return count;
    }

    private boolean checkPattern(BlockPos start, Block[][] pattern, int rotation) {
        BlockPos.Mutable mutable = new BlockPos.Mutable();
        
        for (int y = 0; y < pattern.length; y++) {
            for (int x = 0; x < pattern[y].length; x++) {
                int rotX = rotation % 2 == 0 ? x : pattern[y].length - 1 - x;
                int rotZ = rotation < 2 ? 0 : 1;
                
                mutable.set(start.getX() + rotX, start.getY() - y, start.getZ() + rotZ);
                Block expectedBlock = pattern[y][x];
                
                if (expectedBlock == null) continue; // Skip null (ignored) blocks
                
                Block actualBlock = mc.world.getBlockState(mutable).getBlock();
                if (expectedBlock != actualBlock) return false;
            }
        }
        
        return true;
    }

    private void showToast(String title, String message) {
        if (mc.player != null) {
            mc.player.sendMessage(net.minecraft.text.Text.literal("§d[TerrainAnalyzer]§r " + message), false);
        }
    }

    private void showSuccess(String message) {
        showToast("Success", "§a" + message);
    }

    private void showError(String message) {
        showToast("Error", "§c" + message);
    }

    // Command handler for rule management
    public void handleCommand(String[] args) {
        if (args.length < 1) {
            showToast("Rules", "Available commands: add, remove, list, share");
            return;
        }

        if (!initialized || ruleManager == null) {
            showError("RuleManager not initialized");
            return;
        }

        switch (args[0].toLowerCase()) {
            case "add":
                if (args.length < 4) {
                    showError("Usage: add <name> <description> <author>");
                    return;
                }
                SearchRule rule = new SearchRule();
                rule.name = args[1];
                rule.description = args[2];
                rule.author = args[3];
                ruleManager.addRule(rule);
                showSuccess("Rule added: " + rule.name);
                break;

            case "remove":
                if (args.length < 2) {
                    showError("Usage: remove <name>");
                    return;
                }
                ruleManager.getRules().removeIf(r -> {
                    if (r.name.equals(args[1])) {
                        showSuccess("Rule removed: " + r.name);
                        return true;
                    }
                    return false;
                });
                break;

            case "list":
                List<SearchRule> rules = ruleManager.getRules();
                if (rules.isEmpty()) {
                    showToast("Rules", "No rules defined");
                } else {
                    showToast("Rules", "§7Found " + rules.size() + " rules:");
                    rules.forEach(r -> showToast("Rule", "- " + r.name));
                }
                break;

            case "share":
                if (args.length < 2) {
                    showError("Usage: share <name>");
                    return;
                }
                rules = ruleManager.getRules();
                rules.stream()
                    .filter(r -> r.name.equals(args[1]))
                    .findFirst()
                    .ifPresentOrElse(
                        r -> {
                            if (!ruleShareWebhook.get().isEmpty()) {
                                if (ruleManager.shareRule(r, ruleShareWebhook.get())) {
                                    showSuccess("Rule shared: " + r.name);
                                } else {
                                    showError("Failed to share rule");
                                }
                            } else {
                                showError("Webhook URL not set");
                            }
                        },
                        () -> showError("Rule not found: " + args[1])
                    );
                break;

            default:
                showError("Unknown command: " + args[0]);
        }
    }

    @Override
    public String getInfoString() {
        int customMatches = matchedRules.values().stream()
            .mapToInt(List::size)
            .sum();
        return String.format("%d areas (%d patterns, %d rules)",
            suspiciousAreas.size(),
            detectedPatterns.values().stream().mapToInt(Set::size).sum(),
            customMatches);
    }
}