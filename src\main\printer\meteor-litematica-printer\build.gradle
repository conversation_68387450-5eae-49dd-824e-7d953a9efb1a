plugins {
    id 'fabric-loom' version '1.7.4'
}

sourceCompatibility = targetCompatibility = JavaVersion.VERSION_21

archivesBaseName = project.archives_base_name
version = project.mod_version
group = project.maven_group

repositories {
	mavenCentral()
    maven { url = "https://maven.meteordev.org/releases" }
    maven { url = "https://maven.meteordev.org/snapshots" }
	maven { url 'https://jitpack.io' }

    //maven { url = "https://masa.dy.fi/maven" }


}

dependencies {
    // Minecraft
    minecraft "com.mojang:minecraft:${project.minecraft_version}"
    mappings "net.fabricmc:yarn:${project.yarn_mappings}:v2"
    modImplementation "net.fabricmc:fabric-loader:${project.loader_version}"

    // Meteor
    modImplementation "meteordevelopment:meteor-client:${project.meteor_version}-SNAPSHOT"
	//modImplementation "meteordevelopment:baritone:${project.baritone_version}-SNAPSHOT"

    //litematica
    //modImplementation "curse.maven:litematica-${project.litematica_projectid}:${project.litematica_fileid}"
	modImplementation 'com.github.sakura-ryoko:litematica:1.21.4-0.21.4-sakura.4'
    //modImplementation "fi.dy.masa.malilib:malilib-fabric-${project.minecraft_version}:${project.malilib_version}"
	modImplementation 'com.github.sakura-ryoko:malilib:1.21.4-0.23.3'


}

processResources {
    filesMatching("fabric.mod.json") {
        expand "version": project.version, "mc_version": project.minecraft_version
    }
}

tasks.withType(JavaCompile).configureEach {
    it.options.encoding("UTF-8")
}
