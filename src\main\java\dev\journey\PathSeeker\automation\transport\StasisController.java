package dev.journey.PathSeeker.automation.transport;

import dev.journey.PathSeeker.modules.automation.ShulkerTransportModule;
import dev.journey.PathSeeker.automation.AutomationFlow;
import dev.journey.PathSeeker.automation.conditions.Condition;
import net.minecraft.client.MinecraftClient;
import net.minecraft.entity.projectile.thrown.EnderPearlEntity;
import net.minecraft.util.hit.HitResult;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Box;
import net.minecraft.util.math.Vec3d;

import java.util.List;

/**
 * Controls stasis chamber operations and pearl teleportation
 */
public class StasisController {
    private static final int THROW_RETRY_LIMIT = 3;
    private static final double POSITION_TOLERANCE = 0.1;
    
    private final ShulkerTransportModule module;
    private final TransportConfig config;
    private final MinecraftClient mc;
    
    private EnderPearlEntity activePearl;
    private int throwAttempts;
    private long lastActionTime;
    private BlockPos lastValidPosition;

    public StasisController(ShulkerTransportModule module, TransportConfig config) {
        this.module = module;
        this.config = config;
        this.mc = MinecraftClient.getInstance();
    }

    /**
     * Update controller state
     */
    public void update() {
        if (activePearl != null && !activePearl.isAlive()) {
            activePearl = null;
        }

        if (mc.player != null) {
            lastValidPosition = mc.player.getBlockPos();
        }
    }

    /**
     * Initiate pearl throw sequence
     */
    public TransportAction initiatePearlThrow() {
        return new TransportAction() {
            @Override
            public void execute(AutomationFlow flow) {
                if (!validateState("pearl throw")) {
                    throw TransportException.safety("Invalid state for pearl throw", module.getState());
                }

                // Align player position and rotation
                alignForThrow();
                
                // Attempt pearl throw
                if (!throwPearl()) {
                    if (++throwAttempts >= THROW_RETRY_LIMIT) {
                        throw TransportException.timing("Pearl throw failed after " + THROW_RETRY_LIMIT + " attempts", 
                            module.getState());
                    }
                    // Allow retry
                    flow.setVariable("retryThrow", true);
                }
                
                lastActionTime = System.currentTimeMillis();
            }

            @Override
            public boolean requiresConfirmation() {
                return true;
            }

            @Override
            public void rollback(AutomationFlow flow) {
                if (activePearl != null) {
                    activePearl.discard();
                    activePearl = null;
                }
                throwAttempts = 0;
            }
        };
    }

    /**
     * Prepare stasis chamber
     */
    public TransportAction prepareChamber() {
        return new TransportAction() {
            @Override
            public void execute(AutomationFlow flow) {
                if (!validateState("chamber preparation")) {
                    throw TransportException.safety("Invalid state for chamber preparation", module.getState());
                }

                BlockPos chamberPos = config.chamberPos.get();
                if (!validateChamberPosition(chamberPos)) {
                    throw TransportException.safety("Invalid chamber position", module.getState());
                }

                // Clear any obstructions
                clearChamberArea(chamberPos);
                
                lastActionTime = System.currentTimeMillis();
            }

            @Override
            public boolean requiresConfirmation() {
                return false;
            }

            @Override
            public void rollback(AutomationFlow flow) {
                // No specific rollback needed for preparation
            }
        };
    }

    /**
     * Activate stasis chamber
     */
    public TransportAction activateChamber() {
        return new TransportAction() {
            @Override
            public void execute(AutomationFlow flow) {
                if (!validateState("chamber activation")) {
                    throw TransportException.safety("Invalid state for chamber activation", module.getState());
                }

                BlockPos chamberPos = config.chamberPos.get();
                if (!isPlayerInPosition()) {
                    throw TransportException.safety("Player not in correct position", module.getState());
                }

                // Trigger chamber mechanism
                activateChamberMechanism(chamberPos);
                
                lastActionTime = System.currentTimeMillis();
            }

            @Override
            public boolean requiresConfirmation() {
                return true;
            }

            @Override
            public void rollback(AutomationFlow flow) {
                deactivateChamber();
            }
        };
    }

    /**
     * Emergency cleanup
     */
    public void emergencyCleanup() {
        if (activePearl != null) {
            activePearl.discard();
            activePearl = null;
        }
        deactivateChamber();
        throwAttempts = 0;
    }

    /**
     * Shutdown controller
     */
    public void shutdown() {
        emergencyCleanup();
    }

    private boolean validateState(String operation) {
        if (mc.player == null || mc.world == null) {
            module.error("Player or world not available for " + operation);
            return false;
        }

        // Check if enough time has passed since last action
        if (System.currentTimeMillis() - lastActionTime < 1000) {
            module.warning("Action attempted too quickly");
            return false;
        }

        return true;
    }

    private void alignForThrow() {
        // TODO: Implement precise player positioning and rotation
        // This will need to calculate the optimal angle and position
        // for the pearl throw based on the chamber location
    }

    private boolean throwPearl() {
        if (mc.player == null || mc.world == null) return false;

        try {
            // Find ender pearl in hotbar
            int pearlSlot = findPearlInHotbar();
            if (pearlSlot == -1) {
                module.error("No ender pearls found in hotbar");
                return false;
            }

            // Select pearl
            int previousSlot = mc.player.getInventory().selectedSlot;
            mc.player.getInventory().selectedSlot = pearlSlot;

            // Calculate throw angle
            float pitch = getThrowPitch();
            float yaw = getThrowYaw();
            mc.player.setPitch(pitch);
            mc.player.setYaw(yaw);

            // Execute throw
            mc.options.useKey.setPressed(true);
            mc.options.useKey.setPressed(false);

            // Restore previous slot
            mc.player.getInventory().selectedSlot = previousSlot;

            return true;
        } catch (Exception e) {
            module.error("Failed to throw pearl: " + e.getMessage());
            return false;
        }
    }

    private int findPearlInHotbar() {
        if (mc.player == null) return -1;
        
        for (int i = 0; i < 9; i++) {
            if (mc.player.getInventory().getStack(i).getItem() == net.minecraft.item.Items.ENDER_PEARL) {
                return i;
            }
        }
        return -1;
    }

    private float getThrowPitch() {
        // Get pitch from module settings
        if (config.mode.get() == TransportMode.TRANSPORTER) {
            return module.getPitchSetting();
        }
        return -45.0f; // Default fallback
    }

    private float getThrowYaw() {
        // Get yaw from module settings
        if (config.mode.get() == TransportMode.TRANSPORTER) {
            return module.getYawSetting();
        }
        
        // Calculate based on target if not set
        if (mc.player == null) return 0.0f;
        BlockPos target = config.chamberPos.get();
        double dx = target.getX() - mc.player.getX();
        double dz = target.getZ() - mc.player.getZ();
        return (float) Math.toDegrees(Math.atan2(-dx, dz));
    }

    private boolean validateChamberPosition(BlockPos pos) {
        if (pos == null) return false;

        // Check if chamber position is loaded
        if (!mc.world.isChunkLoaded(pos.getX() >> 4, pos.getZ() >> 4)) {
            return false;
        }

        // Check for required clear space
        double radius = config.safetyRadius.get();
        Box checkArea = new Box(
            pos.getX() - radius, pos.getY() - radius, pos.getZ() - radius,
            pos.getX() + radius, pos.getY() + radius, pos.getZ() + radius
        );

        // TODO: Implement actual chamber validation
        // This will need to verify:
        // 1. Chamber structure integrity
        // 2. Required blocks are present
        // 3. No obstructions
        return true;
    }

    private void clearChamberArea(BlockPos pos) {
        // TODO: Implement chamber area clearing
        // This will need to:
        // 1. Remove any temporary blocks
        // 2. Clear any entities
        // 3. Prepare chamber mechanism
    }

    private boolean isPlayerInPosition() {
        if (mc.player == null || lastValidPosition == null) return false;

        Vec3d playerPos = mc.player.getPos();
        Vec3d targetPos = Vec3d.ofCenter(lastValidPosition);

        return playerPos.distanceTo(targetPos) <= POSITION_TOLERANCE;
    }

    private void activateChamberMechanism(BlockPos pos) {
        if (config.mode.get().isCoordinator()) {
            // Use commander's trapdoor commands
            String closeCmd = "/setblock " + pos.getX() + " " + pos.getY() + " " + pos.getZ() + " minecraft:trapdoor[powered=true]";
            String openCmd = "/setblock " + pos.getX() + " " + pos.getY() + " " + pos.getZ() + " minecraft:trapdoor[powered=false]";
            
            try {
                // Execute chamber activation sequence
                module.executeCommand(closeCmd);
                Thread.sleep(module.getStasisDelay() * 50L); // Convert ticks to ms
                module.executeCommand(openCmd);
            } catch (InterruptedException e) {
                module.error("Chamber activation sequence interrupted");
            }
        }
    }

    private void deactivateChamber() {
        if (config.mode.get().isCoordinator()) {
            BlockPos pos = module.getTrapdoorLocation();
            if (pos != null) {
                // Reset trapdoor to open state
                String cmd = "/setblock " + pos.getX() + " " + pos.getY() + " " + pos.getZ() + " minecraft:trapdoor[powered=false]";
                module.executeCommand(cmd);
            }
        }
    }
}
