package dev.journey.PathSeeker.utils.pathfinding;

import net.minecraft.block.Blocks;
import net.minecraft.client.MinecraftClient;
import net.minecraft.util.math.BlockPos;

import java.util.ArrayList;
import java.util.List;

public class AStar {
    private static boolean check = false;
    private static final MinecraftClient mc = MinecraftClient.getInstance();

    public static class AStarNode {
        public BlockPos pos;
        public BlockPos parent;
        private static final List<AStarNode> nodes = new ArrayList<>();

        public AStarNode(BlockPos pos) {
            this.pos = pos;
            nodes.add(this);
        }

        public static void clearNodes() {
            nodes.clear();
        }

        public static AStarNode getNodeFromBlockPos(BlockPos pos) {
            if (pos == null) return null;
            for (AStarNode node : nodes) {
                if (node.pos.equals(pos)) {
                    return node;
                }
            }
            return null;
        }
    }

    /**
     * Generates a path to the given goal
     * @param start Starting position
     * @param goal Goal position
     * @param positions Nearby positions that can be added to open list
     * @param checkPositions Positions to check for solids
     * @param loopAmount Maximum number of iterations
     * @return Path as list of BlockPos
     */
    public static List<BlockPos> generatePath(BlockPos start, BlockPos goal, BlockPos[] positions, List<BlockPos> checkPositions, int loopAmount) {
        AStarNode.clearNodes();
        BlockPos current = start;
        BlockPos closest = current;
        List<BlockPos> open = new ArrayList<>();
        List<BlockPos> closed = new ArrayList<>();
        int noClosest = 0;

        for (int i = 0; i < loopAmount; i++) {
            // Check if reached goal
            if (current.equals(goal)) {
                check = false;
                return getPath(current);
            }

            // Get position with lowest f cost from open list
            double lowestFCost = Double.MAX_VALUE;
            for (BlockPos pos : open) {
                double fCost = getFCost(pos, goal, start);
                if (fCost < lowestFCost) {
                    lowestFCost = fCost;
                    current = pos;
                }
            }

            // Update lists
            closed.add(current);
            open.remove(current);
            List<BlockPos> newOpen = getOpen(positions, checkPositions, current, start, open, closed);
            if (newOpen != null) {
                open.addAll(newOpen);
            }

            // Update closest position
            if (lowestFCost < getFCost(closest, goal, start)) {
                closest = current;
                noClosest = 0;
            } else {
                noClosest++;
                if (noClosest > 200) break;
            }
        }

        // If no path to goal, try path to closest position
        if (!check) {
            check = true;
            return generatePath(start, closest, positions, checkPositions, loopAmount);
        } else {
            check = false;
            return new ArrayList<>();
        }
    }

    private static List<BlockPos> getOpen(BlockPos[] positions, List<BlockPos> checkPositions, BlockPos current, BlockPos start, List<BlockPos> open, List<BlockPos> closed) {
        List<BlockPos> list = new ArrayList<>();
        List<BlockPos> positions2 = new ArrayList<>();

        // Generate positions to check
        for (BlockPos pos : positions) {
            positions2.add(current.add(pos.getX(), pos.getY(), pos.getZ()));
        }

        // Check each position
        outer: for (BlockPos pos : positions2) {
            if (!mc.world.getBlockState(pos).isAir() && !closed.contains(pos)) {
                List<BlockPos> checkPositions2 = new ArrayList<>();
                for (BlockPos check : checkPositions) {
                    checkPositions2.add(pos.add(check.getX(), check.getY(), check.getZ()));
                }

                // Check surrounding blocks
                for (BlockPos check : checkPositions2) {
                    if (!mc.world.isChunkLoaded(check)) {
                        return null;
                    }
                    if (!mc.world.getBlockState(check).isAir() || !mc.world.isChunkLoaded(check)) {
                        continue outer;
                    }
                    if (mc.world.getBlockState(check).isOf(Blocks.LAVA)) {
                        continue outer;
                    }
                }

                // Update node parent if better path found
                AStarNode n = AStarNode.getNodeFromBlockPos(pos);
                if (n == null) {
                    n = new AStarNode(pos);
                }
                if (!open.contains(pos)) {
                    list.add(pos);
                }
                BlockPos currentParent = n.parent;
                if (currentParent == null || getGCost(current, start) < getGCost(currentParent, start)) {
                    n.parent = current;
                }
            }
        }
        return list;
    }

    private static double getFCost(BlockPos pos, BlockPos goal, BlockPos start) {
        // H cost (straight line to goal)
        double dx = goal.getX() - pos.getX();
        double dz = goal.getZ() - pos.getZ();
        double h = Math.sqrt(dx * dx + dz * dz);
        return getGCost(pos, start) + h;
    }

    private static double getGCost(BlockPos pos, BlockPos start) {
        if (pos == null) return Double.MAX_VALUE;
        double dx = start.getX() - pos.getX();
        double dy = start.getY() - pos.getY();
        double dz = start.getZ() - pos.getZ();
        return Math.sqrt(Math.abs(dx) + Math.abs(dy) + Math.abs(dz));
    }

    private static List<BlockPos> getPath(BlockPos current) {
        List<BlockPos> path = new ArrayList<>();
        try {
            AStarNode n = AStarNode.getNodeFromBlockPos(current);
            if (n == null) {
                return path;
            }
            path.add(n.pos);
            while (n.parent != null) {
                path.add(n.parent);
                n = AStarNode.getNodeFromBlockPos(n.parent);
                if (n == null) break;
            }
        } catch (Exception e) {
            // Path is zero length
        }
        return path;
    }
}