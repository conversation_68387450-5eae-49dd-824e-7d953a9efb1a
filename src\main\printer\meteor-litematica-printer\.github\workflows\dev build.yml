name: Dev_build

on: workflow_dispatch
jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: Setup JDK 21
        uses: actions/setup-java@v3.10.0
        with:
          java-version: 21
          distribution: adopt
      - name: Build
        run: |
          chmod +x ./gradlew
          ./gradlew build
      - name: Create Dev Build release
        uses: marvinpinto/action-automatic-releases@latest
        with:
          repo_token: '${{ secrets.GITHUB_TOKEN }}'
          automatic_release_tag: "mc_1.21_meteor_0.5.8_ver_1.3"
          prerelease: false
          title: "Version 1.3 for meteor 0.5.8 and Minecraft 1.21"
          files: |
            ./build/libs/*.jar
