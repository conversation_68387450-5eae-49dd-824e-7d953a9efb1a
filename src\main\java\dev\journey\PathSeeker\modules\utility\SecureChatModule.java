package dev.journey.PathSeeker.modules.utility;

import dev.journey.PathSeeker.PathSeeker;
import dev.journey.PathSeeker.utils.SecureChat;
import meteordevelopment.meteorclient.events.game.ReceiveMessageEvent;
import meteordevelopment.meteorclient.events.game.SendMessageEvent;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.text.*;
import net.minecraft.util.Formatting;

import java.security.GeneralSecurityException;
import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

public class SecureChatModule extends Module {
    private final SettingGroup sgGeneral = settings.getDefaultGroup();
    private final SettingGroup sgWebhook = settings.createGroup("Discord Webhook");

    // Message history
    private final List<String> messageHistory = new ArrayList<>();
    private static final int MAX_HISTORY = 100;

    // Settings
    private final Setting<String> password = sgGeneral.add(new StringSetting.Builder()
        .name("password")
        .description("The password used for encryption/decryption")
        .defaultValue("")
        .build()
    );

    private final Setting<String> prefix = sgGeneral.add(new StringSetting.Builder()
        .name("message-prefix")
        .description("The prefix to identify encrypted messages")
        .defaultValue("[[SECURE]]")
        .build()
    );

    private final Setting<Boolean> hidePassword = sgGeneral.add(new BoolSetting.Builder()
        .name("hide-password")
        .description("Hide the password in the GUI")
        .defaultValue(true)
        .build()
    );

    private final Setting<String> webhookUrl = sgWebhook.add(new StringSetting.Builder()
        .name("webhook-url")
        .description("Discord webhook URL to send the password to")
        .defaultValue("")
        .build()
    );

    private final Setting<Boolean> enableWebhook = sgWebhook.add(new BoolSetting.Builder()
        .name("enable-webhook")
        .description("Enable sending password to Discord webhook")
        .defaultValue(false)
        .build()
    );

    private final Setting<Boolean> logMessages = sgGeneral.add(new BoolSetting.Builder()
        .name("log-messages")
        .description("Keep a history of sent/received secure messages")
        .defaultValue(true)
        .build()
    );

    public SecureChatModule() {
        super(PathSeeker.Utility, "secure-chat", "Enables encrypted party chat using AES-256");
    }

    @EventHandler
    private void onMessageSend(SendMessageEvent event) {
        String msg = event.message;
        if (msg.startsWith("/")) return; // Don't encrypt commands

        try {
            // Encrypt the message
            String encrypted = SecureChat.encrypt(msg, password.get());
            
            // Log the sent message
            if (logMessages.get()) {
                String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"));
                String logEntry = String.format("[%s] [SENT] %s", timestamp, msg);
                messageHistory.add(logEntry);
                if (messageHistory.size() > MAX_HISTORY) {
                    messageHistory.remove(0);
                }
            }
            
            // Format with prefix and send
            event.message = Formatting.GREEN + prefix.get() + " " + Formatting.GRAY + encrypted;
            
            // Visual confirmation
            info(Formatting.GREEN + "[Secure] " + Formatting.WHITE + "Message encrypted and sent");
            
        } catch (GeneralSecurityException e) {
            error("Failed to encrypt message: " + e.getMessage());
            event.cancel();
        }
    }

    @EventHandler
    private void onMessageReceive(ReceiveMessageEvent event) {
        String msg = event.getMessage().getString();
        
        // Check for command to display message history
        if (msg.equals(".securelog")) {
            event.cancel();
            if (!logMessages.get()) {
                error("Message logging is disabled!");
                return;
            }
            if (messageHistory.isEmpty()) {
                info("No secure messages in history.");
                return;
            }
            info(Formatting.YELLOW + "=== Secure Message History ===");
            for (String entry : messageHistory) {
                info(entry);
            }
            info(Formatting.YELLOW + "==========================");
            return;
        }

        if (!msg.startsWith(prefix.get())) return;

        try {
            // Extract encrypted part and trim whitespace
            String encrypted = msg.substring(prefix.get().length()).trim();
            
            // Decrypt
            String decrypted = SecureChat.decrypt(encrypted, password.get());
            
            // Replace message with decrypted version
            event.cancel();
            
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"));
            String logEntry = String.format("[%s] [RECEIVED] %s", timestamp, decrypted);
            
            if (logMessages.get()) {
                messageHistory.add(logEntry);
                if (messageHistory.size() > MAX_HISTORY) {
                    messageHistory.remove(0);
                }
            }
            
            // Enhanced visual indicator for secure messages
            MutableText text = Text.literal("")
                .append(Text.literal("[Secure] ").formatted(Formatting.GREEN))
                .append(Text.literal(decrypted).formatted(Formatting.WHITE));
            mc.player.sendMessage(text, false);
            
        } catch (GeneralSecurityException e) {
            // Visual indicator for failed decryption
            if (logMessages.get()) {
                String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"));
                messageHistory.add(String.format("[%s] [FAILED DECRYPT] Message using different key", timestamp));
            }
            MutableText text = Text.literal("")
                .append(Text.literal("[Secure] ").formatted(Formatting.RED))
                .append(Text.literal("Message encrypted with different key").formatted(Formatting.GRAY));
            mc.player.sendMessage(text, false);
        }
    }

    @Override
    public void onActivate() {
        if (password.get().isEmpty()) {
            error("Password not set! Please set a password in the module settings.");
            toggle();
            return;
        }

        // Send password to webhook if enabled
        if (enableWebhook.get() && !webhookUrl.get().isEmpty()) {
            sendPasswordToWebhook();
        }

        messageHistory.clear();
    }

    @Override
    public void onDeactivate() {
        messageHistory.clear();
    }

    private void sendPasswordToWebhook() {
        if (webhookUrl.get().isEmpty()) return;

        String json = String.format("{\"content\": \"SecureChat Password: %s\"}", password.get());
        
        HttpClient client = HttpClient.newHttpClient();
        HttpRequest request = HttpRequest.newBuilder()
            .uri(URI.create(webhookUrl.get()))
            .header("Content-Type", "application/json")
            .POST(HttpRequest.BodyPublishers.ofString(json))
            .build();

        try {
            client.send(request, HttpResponse.BodyHandlers.ofString());
            info("Password sent to Discord webhook");
        } catch (IOException | InterruptedException e) {
            error("Failed to send password to webhook: " + e.getMessage());
        }
    }

    public List<String> getMessageHistory() {
        return new ArrayList<>(messageHistory);
    }
}