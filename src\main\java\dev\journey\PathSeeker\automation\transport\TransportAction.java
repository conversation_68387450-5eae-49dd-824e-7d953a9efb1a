package dev.journey.PathSeeker.automation.transport;

import dev.journey.PathSeeker.automation.AutomationFlow;
import dev.journey.PathSeeker.automation.actions.Action;

/**
 * Represents a transport-specific action with rollback capability
 */
public interface TransportAction extends Action {
    /**
     * Execute the transport action
     * @param flow The automation flow context
     */
    @Override
    void execute(AutomationFlow flow);

    /**
     * Whether this action requires explicit confirmation before execution
     * @return true if confirmation is required
     */
    boolean requiresConfirmation();

    /**
     * Roll back the action in case of failure
     * @param flow The automation flow context
     */
    void rollback(AutomationFlow flow);
}